{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-anon-key';\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-service-role-key';\n\n// 检查环境变量是否正确配置\nconst isConfigured =\n  process.env.NEXT_PUBLIC_SUPABASE_URL &&\n  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&\n  process.env.SUPABASE_SERVICE_ROLE_KEY &&\n  !process.env.NEXT_PUBLIC_SUPABASE_URL.includes('placeholder') &&\n  !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.includes('placeholder') &&\n  !process.env.SUPABASE_SERVICE_ROLE_KEY.includes('placeholder');\n\n// 客户端 Supabase 实例（用于前端）\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// 服务端 Supabase 实例（用于服务端操作，具有更高权限）\nexport const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);\n\n// 导出配置状态\nexport { isConfigured };\n\n// 数据库表名常量\nexport const TABLES = {\n  USERS: 'users',\n  USER_SETTINGS: 'user_settings',\n  DIET_RECORDS: 'diet_records',\n  INGREDIENTS: 'ingredients',\n  DISHES: 'dishes',\n} as const;\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AACrE,MAAM,yBAAyB,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAExE,eAAe;AACf,MAAM,eACJ,wUAEA,QAAQ,GAAG,CAAC,yBAAyB,IACrC,CAAC,6EAAqC,QAAQ,CAAC,kBAC/C,CAAC,qPAA0C,QAAQ,CAAC,kBACpD,CAAC,QAAQ,GAAG,CAAC,yBAAyB,CAAC,QAAQ,CAAC;AAG3C,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;;AAMhD,MAAM,SAAS;IACpB,OAAO;IACP,eAAe;IACf,cAAc;IACd,aAAa;IACb,QAAQ;AACV", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/lib/database.ts"], "sourcesContent": ["import { supabase, supabaseAdmin, TABLES, isConfigured } from './supabase';\nimport { User, UserSettings, DietRecord, Ingredient, Dish } from '@/types';\nimport bcrypt from 'bcryptjs';\n\n// 用户相关操作\nexport class UserService {\n  // 创建用户\n  static async createUser(userData: {\n    username: string;\n    email?: string;\n    password: string;\n    role?: 'admin' | 'user';\n    auth_code?: string;\n  }): Promise<User> {\n    if (!isConfigured) {\n      throw new Error('数据库未配置，请设置正确的环境变量');\n    }\n    const passwordHash = await bcrypt.hash(userData.password, 12);\n\n    const { data, error } = await supabaseAdmin\n      .from(TABLES.USERS)\n      .insert({\n        username: userData.username,\n        email: userData.email || '',\n        password_hash: passwordHash,\n        role: userData.role || 'user',\n        auth_code: userData.auth_code,\n      })\n      .select('id, username, email, role, auth_code, created_at, updated_at')\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  // 验证用户登录\n  static async validateUser(username: string, password: string): Promise<User | null> {\n    const { data, error } = await supabaseAdmin\n      .from(TABLES.USERS)\n      .select('*')\n      .eq('username', username)\n      .single();\n\n    if (error || !data) return null;\n\n    const isValid = await bcrypt.compare(password, data.password_hash);\n    if (!isValid) return null;\n\n    // 返回不包含密码的用户信息\n    const { password_hash: _, ...userInfo } = data;\n    return userInfo as User;\n  }\n\n  // 获取用户信息\n  static async getUserById(id: string): Promise<User | null> {\n    const { data, error } = await supabase\n      .from(TABLES.USERS)\n      .select('id, username, email, role, auth_code, created_at, updated_at')\n      .eq('id', id)\n      .single();\n\n    if (error) return null;\n    return data;\n  }\n\n  // 获取所有用户（管理员功能）\n  static async getAllUsers(): Promise<User[]> {\n    const { data, error } = await supabaseAdmin\n      .from(TABLES.USERS)\n      .select('id, username, email, role, auth_code, created_at, updated_at')\n      .order('created_at', { ascending: false });\n\n    if (error) throw error;\n    return data || [];\n  }\n\n  // 更新用户信息\n  static async updateUser(id: string, updates: Partial<User>): Promise<User> {\n    const { data, error } = await supabaseAdmin\n      .from(TABLES.USERS)\n      .update(updates)\n      .eq('id', id)\n      .select('id, username, email, role, auth_code, created_at, updated_at')\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  // 重置用户密码\n  static async resetPassword(id: string, newPassword: string): Promise<void> {\n    const passwordHash = await bcrypt.hash(newPassword, 12);\n    \n    const { error } = await supabaseAdmin\n      .from(TABLES.USERS)\n      .update({ password_hash: passwordHash })\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n\n  // 删除用户\n  static async deleteUser(id: string): Promise<void> {\n    const { error } = await supabaseAdmin\n      .from(TABLES.USERS)\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n}\n\n// 用户设置相关操作\nexport class UserSettingsService {\n  // 获取用户设置\n  static async getUserSettings(userId: string): Promise<UserSettings | null> {\n    const { data, error } = await supabase\n      .from(TABLES.USER_SETTINGS)\n      .select('*')\n      .eq('user_id', userId)\n      .single();\n\n    if (error) {\n      // 如果没有设置记录，创建默认设置\n      if (error.code === 'PGRST116') {\n        const defaultSettings = {\n          user_id: userId,\n          ai_model: 'gpt-4o',\n          api_base_url: process.env.DEFAULT_AI_BASE_URL || 'https://api.openai.com',\n          api_key: process.env.DEFAULT_AI_API_KEY || '',\n        };\n\n        const { data: newSettings, error: createError } = await supabase\n          .from(TABLES.USER_SETTINGS)\n          .insert(defaultSettings)\n          .select()\n          .single();\n\n        if (createError) return null;\n        return newSettings;\n      }\n      return null;\n    }\n    return data;\n  }\n\n  // 更新用户设置\n  static async updateUserSettings(userId: string, settings: Partial<UserSettings>): Promise<UserSettings> {\n    // 先检查是否存在设置记录\n    const { data: existingSettings } = await supabase\n      .from(TABLES.USER_SETTINGS)\n      .select('id')\n      .eq('user_id', userId)\n      .single();\n\n    if (existingSettings) {\n      // 如果存在，则更新\n      const { data, error } = await supabase\n        .from(TABLES.USER_SETTINGS)\n        .update(settings)\n        .eq('user_id', userId)\n        .select()\n        .single();\n\n      if (error) throw error;\n      return data;\n    } else {\n      // 如果不存在，则插入\n      const { data, error } = await supabase\n        .from(TABLES.USER_SETTINGS)\n        .insert({\n          user_id: userId,\n          ...settings,\n        })\n        .select()\n        .single();\n\n      if (error) throw error;\n      return data;\n    }\n  }\n}\n\n// 饮食记录相关操作\nexport class DietRecordService {\n  // 创建饮食记录\n  static async createDietRecord(recordData: {\n    user_id: string;\n    original_input: string;\n    input_type: 'text' | 'image';\n    image_url?: string;\n    meal_type: string;\n    record_time?: string;\n    total_calories: number;\n    ai_analysis: string;\n    dishes: string[];\n    ingredients: Omit<Ingredient, 'id' | 'diet_record_id' | 'created_at'>[];\n  }): Promise<DietRecord> {\n    // 创建饮食记录\n    const { data: record, error: recordError } = await supabase\n      .from(TABLES.DIET_RECORDS)\n      .insert({\n        user_id: recordData.user_id,\n        original_input: recordData.original_input,\n        input_type: recordData.input_type,\n        image_url: recordData.image_url,\n        meal_type: recordData.meal_type,\n        record_time: recordData.record_time,\n        total_calories: recordData.total_calories,\n        ai_analysis: recordData.ai_analysis,\n        dishes: recordData.dishes,\n      })\n      .select()\n      .single();\n\n    if (recordError) throw recordError;\n\n    // 创建食材记录\n    if (recordData.ingredients.length > 0) {\n      const ingredientsData = recordData.ingredients.map(ingredient => ({\n        ...ingredient,\n        diet_record_id: record.id,\n      }));\n\n      const { error: ingredientsError } = await supabase\n        .from(TABLES.INGREDIENTS)\n        .insert(ingredientsData);\n\n      if (ingredientsError) throw ingredientsError;\n    }\n\n    return record;\n  }\n\n  // 获取用户的饮食记录\n  static async getUserDietRecords(userId: string, limit = 50, offset = 0): Promise<DietRecord[]> {\n    const { data, error } = await supabase\n      .from(TABLES.DIET_RECORDS)\n      .select(`\n        *,\n        ingredients (*)\n      `)\n      .eq('user_id', userId)\n      .order('record_time', { ascending: false })\n      .range(offset, offset + limit - 1);\n\n    if (error) throw error;\n    return data || [];\n  }\n\n  // 删除饮食记录\n  static async deleteDietRecord(id: string, userId: string): Promise<void> {\n    const { error } = await supabase\n      .from(TABLES.DIET_RECORDS)\n      .delete()\n      .eq('id', id)\n      .eq('user_id', userId);\n\n    if (error) throw error;\n  }\n}\n\n// 系统设置相关操作\nexport class SystemSettingsService {\n  // 获取系统设置\n  static async getSetting(key: string): Promise<string | null> {\n    const { data, error } = await supabase\n      .from('system_settings')\n      .select('value')\n      .eq('key', key)\n      .single();\n\n    if (error) return null;\n    return data?.value || null;\n  }\n\n  // 更新系统设置\n  static async updateSetting(key: string, value: string): Promise<void> {\n    const { error } = await supabaseAdmin\n      .from('system_settings')\n      .upsert({\n        key,\n        value,\n      });\n\n    if (error) throw error;\n  }\n\n  // 获取所有系统设置\n  static async getAllSettings(): Promise<Record<string, string>> {\n    const { data, error } = await supabase\n      .from('system_settings')\n      .select('key, value');\n\n    if (error) throw error;\n\n    const settings: Record<string, string> = {};\n    data?.forEach(item => {\n      settings[item.key] = item.value;\n    });\n\n    return settings;\n  }\n}\n\n// 菜品库相关操作\nexport class DishService {\n  // 添加菜品到库中\n  static async addDishes(dishes: { name: string; category: string }[]): Promise<void> {\n    const { error } = await supabase\n      .from(TABLES.DISHES)\n      .upsert(dishes, { onConflict: 'name,category' });\n\n    if (error) throw error;\n  }\n\n  // 获取所有菜品\n  static async getAllDishes(): Promise<Dish[]> {\n    const { data, error } = await supabase\n      .from(TABLES.DISHES)\n      .select('*')\n      .order('name');\n\n    if (error) throw error;\n    return data || [];\n  }\n\n  // 搜索菜品\n  static async searchDishes(query: string): Promise<Dish[]> {\n    const { data, error } = await supabase\n      .from(TABLES.DISHES)\n      .select('*')\n      .ilike('name', `%${query}%`)\n      .order('name')\n      .limit(20);\n\n    if (error) throw error;\n    return data || [];\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAEA;;;;;;;AAGO,MAAM;IACX,OAAO;IACP,aAAa,WAAW,QAMvB,EAAiB;QAChB,IAAI,CAAC,wHAAA,CAAA,eAAY,EAAE;YACjB,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,eAAe,MAAM,gHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE;QAE1D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC;YACN,UAAU,SAAS,QAAQ;YAC3B,OAAO,SAAS,KAAK,IAAI;YACzB,eAAe;YACf,MAAM,SAAS,IAAI,IAAI;YACvB,WAAW,SAAS,SAAS;QAC/B,GACC,MAAM,CAAC,gEACP,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,SAAS;IACT,aAAa,aAAa,QAAgB,EAAE,QAAgB,EAAwB;QAClF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,KACP,EAAE,CAAC,YAAY,UACf,MAAM;QAET,IAAI,SAAS,CAAC,MAAM,OAAO;QAE3B,MAAM,UAAU,MAAM,gHAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,aAAa;QACjE,IAAI,CAAC,SAAS,OAAO;QAErB,eAAe;QACf,MAAM,EAAE,eAAe,CAAC,EAAE,GAAG,UAAU,GAAG;QAC1C,OAAO;IACT;IAEA,SAAS;IACT,aAAa,YAAY,EAAU,EAAwB;QACzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,gEACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,OAAO;QAClB,OAAO;IACT;IAEA,gBAAgB;IAChB,aAAa,cAA+B;QAC1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,gEACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,SAAS;IACT,aAAa,WAAW,EAAU,EAAE,OAAsB,EAAiB;QACzE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,CAAC,gEACP,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,SAAS;IACT,aAAa,cAAc,EAAU,EAAE,WAAmB,EAAiB;QACzE,MAAM,eAAe,MAAM,gHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,aAAa;QAEpD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CAClC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC;YAAE,eAAe;QAAa,GACrC,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,OAAO;IACP,aAAa,WAAW,EAAU,EAAiB;QACjD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CAClC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;AACF;AAGO,MAAM;IACX,SAAS;IACT,aAAa,gBAAgB,MAAc,EAAgC;QACzE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,MAAM;QAET,IAAI,OAAO;YACT,kBAAkB;YAClB,IAAI,MAAM,IAAI,KAAK,YAAY;gBAC7B,MAAM,kBAAkB;oBACtB,SAAS;oBACT,UAAU;oBACV,cAAc,QAAQ,GAAG,CAAC,mBAAmB,IAAI;oBACjD,SAAS,QAAQ,GAAG,CAAC,kBAAkB,IAAI;gBAC7C;gBAEA,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC7D,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC,iBACP,MAAM,GACN,MAAM;gBAET,IAAI,aAAa,OAAO;gBACxB,OAAO;YACT;YACA,OAAO;QACT;QACA,OAAO;IACT;IAEA,SAAS;IACT,aAAa,mBAAmB,MAAc,EAAE,QAA+B,EAAyB;QACtG,cAAc;QACd,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC9C,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,MAAM;QAET,IAAI,kBAAkB;YACpB,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC,UACP,EAAE,CAAC,WAAW,QACd,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT,OAAO;YACL,YAAY;YACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC;gBACN,SAAS;gBACT,GAAG,QAAQ;YACb,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;IACF;AACF;AAGO,MAAM;IACX,SAAS;IACT,aAAa,iBAAiB,UAW7B,EAAuB;QACtB,SAAS;QACT,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACxD,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,YAAY,EACxB,MAAM,CAAC;YACN,SAAS,WAAW,OAAO;YAC3B,gBAAgB,WAAW,cAAc;YACzC,YAAY,WAAW,UAAU;YACjC,WAAW,WAAW,SAAS;YAC/B,WAAW,WAAW,SAAS;YAC/B,aAAa,WAAW,WAAW;YACnC,gBAAgB,WAAW,cAAc;YACzC,aAAa,WAAW,WAAW;YACnC,QAAQ,WAAW,MAAM;QAC3B,GACC,MAAM,GACN,MAAM;QAET,IAAI,aAAa,MAAM;QAEvB,SAAS;QACT,IAAI,WAAW,WAAW,CAAC,MAAM,GAAG,GAAG;YACrC,MAAM,kBAAkB,WAAW,WAAW,CAAC,GAAG,CAAC,CAAA,aAAc,CAAC;oBAChE,GAAG,UAAU;oBACb,gBAAgB,OAAO,EAAE;gBAC3B,CAAC;YAED,MAAM,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC/C,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,WAAW,EACvB,MAAM,CAAC;YAEV,IAAI,kBAAkB,MAAM;QAC9B;QAEA,OAAO;IACT;IAEA,YAAY;IACZ,aAAa,mBAAmB,MAAc,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAyB;QAC7F,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,YAAY,EACxB,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,eAAe;YAAE,WAAW;QAAM,GACxC,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAElC,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,SAAS;IACT,aAAa,iBAAiB,EAAU,EAAE,MAAc,EAAiB;QACvE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,YAAY,EACxB,MAAM,GACN,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,WAAW;QAEjB,IAAI,OAAO,MAAM;IACnB;AACF;AAGO,MAAM;IACX,SAAS;IACT,aAAa,WAAW,GAAW,EAA0B;QAC3D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,mBACL,MAAM,CAAC,SACP,EAAE,CAAC,OAAO,KACV,MAAM;QAET,IAAI,OAAO,OAAO;QAClB,OAAO,MAAM,SAAS;IACxB;IAEA,SAAS;IACT,aAAa,cAAc,GAAW,EAAE,KAAa,EAAiB;QACpE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CAClC,IAAI,CAAC,mBACL,MAAM,CAAC;YACN;YACA;QACF;QAEF,IAAI,OAAO,MAAM;IACnB;IAEA,WAAW;IACX,aAAa,iBAAkD;QAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,mBACL,MAAM,CAAC;QAEV,IAAI,OAAO,MAAM;QAEjB,MAAM,WAAmC,CAAC;QAC1C,MAAM,QAAQ,CAAA;YACZ,QAAQ,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK;QACjC;QAEA,OAAO;IACT;AACF;AAGO,MAAM;IACX,UAAU;IACV,aAAa,UAAU,MAA4C,EAAiB;QAClF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,MAAM,EAClB,MAAM,CAAC,QAAQ;YAAE,YAAY;QAAgB;QAEhD,IAAI,OAAO,MAAM;IACnB;IAEA,SAAS;IACT,aAAa,eAAgC;QAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,MAAM,EAClB,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,OAAO;IACP,aAAa,aAAa,KAAa,EAAmB;QACxD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,MAAM,EAClB,MAAM,CAAC,KACP,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAC1B,KAAK,CAAC,QACN,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;AACF", "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { UserService } from '@/lib/database';\nimport { z } from 'zod';\n\nconst loginSchema = z.object({\n  username: z.string().min(1, '用户名不能为空'),\n  password: z.string().min(1, '密码不能为空'),\n});\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    \n    // 验证请求数据\n    const { username, password } = loginSchema.parse(body);\n\n    // 验证用户\n    const user = await UserService.validateUser(username, password);\n    \n    if (!user) {\n      return NextResponse.json(\n        { success: false, error: '用户名或密码错误' },\n        { status: 401 }\n      );\n    }\n\n    // 移除敏感信息 - 从validateUser返回的数据已经不包含password_hash\n    return NextResponse.json({\n      success: true,\n      data: user,\n      message: '登录成功',\n    });\n\n  } catch (error: unknown) {\n    console.error('登录错误:', error);\n    \n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { success: false, error: error.issues[0].message },\n        { status: 400 }\n      );\n    }\n\n    return NextResponse.json(\n      { success: false, error: '登录失败，请稍后重试' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;;;;;AAEA,MAAM,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,SAAS;QACT,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,YAAY,KAAK,CAAC;QAEjD,OAAO;QACP,MAAM,OAAO,MAAM,wHAAA,CAAA,cAAW,CAAC,YAAY,CAAC,UAAU;QAEtD,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,gDAAgD;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAgB;QACvB,QAAQ,KAAK,CAAC,SAAS;QAEvB,IAAI,iBAAiB,+KAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO,MAAM,MAAM,CAAC,EAAE,CAAC,OAAO;YAAC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAa,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
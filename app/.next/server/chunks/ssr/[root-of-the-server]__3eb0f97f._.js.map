{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/auth/AuthGuard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuthGuard = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthGuard() from the server but AuthGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/AuthGuard.tsx <module evaluation>\",\n    \"AuthGuard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,mEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/auth/AuthGuard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuthGuard = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthGuard() from the server but AuthGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/AuthGuard.tsx\",\n    \"AuthGuard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/admin/AdminDashboard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AdminDashboard = registerClientReference(\n    function() { throw new Error(\"Attempted to call AdminDashboard() from the server but AdminDashboard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/admin/AdminDashboard.tsx <module evaluation>\",\n    \"AdminDashboard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yEACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/admin/AdminDashboard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AdminDashboard = registerClientReference(\n    function() { throw new Error(\"Attempted to call AdminDashboard() from the server but AdminDashboard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/admin/AdminDashboard.tsx\",\n    \"AdminDashboard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qDACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/app/admin/page.tsx"], "sourcesContent": ["import { AuthGuard } from '@/components/auth/AuthGuard';\nimport { AdminDashboard } from '@/components/admin/AdminDashboard';\n\nexport default function AdminPage() {\n  return (\n    <AuthGuard requireAuth={true} requireAdmin={true}>\n      <div className=\"min-h-screen bg-gray-50\">\n        <header className=\"bg-white shadow-sm border-b\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center h-16\">\n              <div className=\"flex items-center\">\n                <h1 className=\"text-xl font-semibold text-gray-900\">\n                  管理员控制台\n                </h1>\n              </div>\n              \n              <div className=\"flex items-center space-x-4\">\n                <a\n                  href=\"/dashboard\"\n                  className=\"text-sm text-gray-600 hover:text-gray-900\"\n                >\n                  返回主页\n                </a>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <AdminDashboard />\n        </main>\n      </div>\n    </AuthGuard>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC,uIAAA,CAAA,YAAS;QAAC,aAAa;QAAM,cAAc;kBAC1C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAO,WAAU;8BAChB,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;8CAKtD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQT,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC,6IAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/auth/AuthGuard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuthGuard = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthGuard() from the server but AuthGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/AuthGuard.tsx <module evaluation>\",\n    \"AuthGuard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,mEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/auth/AuthGuard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuthGuard = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthGuard() from the server but AuthGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/AuthGuard.tsx\",\n    \"AuthGuard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/dashboard/DashboardContent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardContent() from the server but DashboardContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/DashboardContent.tsx <module evaluation>\",\n    \"DashboardContent\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+EACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/dashboard/DashboardContent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardContent() from the server but DashboardContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/DashboardContent.tsx\",\n    \"DashboardContent\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2DACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/app/dashboard/page.tsx"], "sourcesContent": ["import { AuthGuard } from '@/components/auth/AuthGuard';\nimport { DashboardContent } from '@/components/dashboard/DashboardContent';\n\nexport default function DashboardPage() {\n  return (\n    <AuthGuard requireAuth={true}>\n      <DashboardContent />\n    </AuthGuard>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC,uIAAA,CAAA,YAAS;QAAC,aAAa;kBACtB,cAAA,8OAAC,mJAAA,CAAA,mBAAgB;;;;;;;;;;AAGvB", "debugId": null}}]}
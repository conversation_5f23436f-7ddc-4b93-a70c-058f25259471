(()=>{var a={};a.id=916,a.ids=[916],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{AM:()=>i,DL:()=>h,Qx:()=>l,cT:()=>k,d2:()=>j});var e=c(56621),f=c(93139),g=a([f]);f=(g.then?(await g)():g)[0];class h{static async createUser(a){if(!e.jD)throw Error("数据库未配置，请设置正确的环境变量");let b=await f.default.hash(a.password,12),{data:c,error:d}=await e.E2.from(e.CG.USERS).insert({username:a.username,email:a.email||"",password_hash:b,role:a.role||"user",auth_code:a.auth_code}).select("id, username, email, role, auth_code, created_at, updated_at").single();if(d)throw d;return c}static async validateUser(a,b){let{data:c,error:d}=await e.E2.from(e.CG.USERS).select("*").eq("username",a).single();if(d||!c||!await f.default.compare(b,c.password_hash))return null;let{password_hash:g,...h}=c;return h}static async getUserById(a){let{data:b,error:c}=await e.ND.from(e.CG.USERS).select("id, username, email, role, auth_code, created_at, updated_at").eq("id",a).single();return c?null:b}static async getAllUsers(){let{data:a,error:b}=await e.E2.from(e.CG.USERS).select("id, username, email, role, auth_code, created_at, updated_at").order("created_at",{ascending:!1});if(b)throw b;return a||[]}static async updateUser(a,b){let{data:c,error:d}=await e.E2.from(e.CG.USERS).update(b).eq("id",a).select("id, username, email, role, auth_code, created_at, updated_at").single();if(d)throw d;return c}static async resetPassword(a,b){let c=await f.default.hash(b,12),{error:d}=await e.E2.from(e.CG.USERS).update({password_hash:c}).eq("id",a);if(d)throw d}static async deleteUser(a){let{error:b}=await e.E2.from(e.CG.USERS).delete().eq("id",a);if(b)throw b}}class i{static async getUserSettings(a){let{data:b,error:c}=await e.ND.from(e.CG.USER_SETTINGS).select("*").eq("user_id",a).single();if(c){if("PGRST116"===c.code){let b={user_id:a,ai_model:"gpt-4o",api_base_url:process.env.DEFAULT_AI_BASE_URL||"https://api.openai.com",api_key:process.env.DEFAULT_AI_API_KEY||""},{data:c,error:d}=await e.ND.from(e.CG.USER_SETTINGS).insert(b).select().single();return d?null:c}return null}return b}static async updateUserSettings(a,b){let{data:c}=await e.ND.from(e.CG.USER_SETTINGS).select("id").eq("user_id",a).single();if(c){let{data:c,error:d}=await e.ND.from(e.CG.USER_SETTINGS).update(b).eq("user_id",a).select().single();if(d)throw d;return c}{let{data:c,error:d}=await e.ND.from(e.CG.USER_SETTINGS).insert({user_id:a,...b}).select().single();if(d)throw d;return c}}}class j{static async createDietRecord(a){let{data:b,error:c}=await e.ND.from(e.CG.DIET_RECORDS).insert({user_id:a.user_id,original_input:a.original_input,input_type:a.input_type,image_url:a.image_url,meal_type:a.meal_type,record_time:a.record_time,total_calories:a.total_calories,ai_analysis:a.ai_analysis,dishes:a.dishes}).select().single();if(c)throw c;if(a.ingredients.length>0){let c=a.ingredients.map(a=>({...a,diet_record_id:b.id})),{error:d}=await e.ND.from(e.CG.INGREDIENTS).insert(c);if(d)throw d}return b}static async getUserDietRecords(a,b=50,c=0){let{data:d,error:f}=await e.ND.from(e.CG.DIET_RECORDS).select(`
        *,
        ingredients (*)
      `).eq("user_id",a).order("record_time",{ascending:!1}).range(c,c+b-1);if(f)throw f;return d||[]}static async deleteDietRecord(a,b){let{error:c}=await e.ND.from(e.CG.DIET_RECORDS).delete().eq("id",a).eq("user_id",b);if(c)throw c}}class k{static async getSetting(a){let{data:b,error:c}=await e.ND.from("system_settings").select("value").eq("key",a).single();return c?null:b?.value||null}static async updateSetting(a,b){let{error:c}=await e.E2.from("system_settings").upsert({key:a,value:b});if(c)throw c}static async getAllSettings(){let{data:a,error:b}=await e.ND.from("system_settings").select("key, value");if(b)throw b;let c={};return a?.forEach(a=>{c[a.key]=a.value}),c}}class l{static async addDishes(a){let{error:b}=await e.ND.from(e.CG.DISHES).upsert(a,{onConflict:"name,category"});if(b)throw b}static async getAllDishes(){let{data:a,error:b}=await e.ND.from(e.CG.DISHES).select("*").order("name");if(b)throw b;return a||[]}static async searchDishes(a){let{data:b,error:c}=await e.ND.from(e.CG.DISHES).select("*").ilike("name",`%${a}%`).order("name").limit(20);if(c)throw c;return b||[]}}d()}catch(a){d(a)}})},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},12412:a=>{"use strict";a.exports=require("assert")},21820:a=>{"use strict";a.exports=require("os")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28505:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{handler:()=>x,patchFetch:()=>w,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(77988),v=a([u]);u=(v.then?(await v)():v)[0];let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/ai/analyze-diet/route",pathname:"/api/ai/analyze-diet",filename:"route",bundlePath:"app/api/ai/analyze-diet/route"},distDir:".next",projectDir:"",resolvedPagePath:"/root/test/l-weight/app/src/app/api/ai/analyze-diet/route.ts",nextConfigOutput:"standalone",userland:u}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function w(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function x(a,b,c){var d;let e="/api/ai/analyze-diet/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G=D,G="/index"===G?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}d()}catch(a){d(a)}})},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},56621:(a,b,c)=>{"use strict";c.d(b,{CG:()=>j,E2:()=>i,ND:()=>h,jD:()=>g});var d=c(66437);let e="https://bkgnzxuasasrzvcuwbfh.supabase.co",f=process.env.SUPABASE_SERVICE_ROLE_KEY||"placeholder-service-role-key",g=process.env.SUPABASE_SERVICE_ROLE_KEY&&!"https://bkgnzxuasasrzvcuwbfh.supabase.co".includes("placeholder")&&!"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJrZ256eHVhc2Fzcnp2Y3V3YmZoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5MzEwNTYsImV4cCI6MjA2OTUwNzA1Nn0.eFq5bPZzHH-kTRKKVmv9NACXycjpziPsRTDzkuy-dVU".includes("placeholder")&&!process.env.SUPABASE_SERVICE_ROLE_KEY.includes("placeholder"),h=(0,d.UU)(e,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJrZ256eHVhc2Fzcnp2Y3V3YmZoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5MzEwNTYsImV4cCI6MjA2OTUwNzA1Nn0.eFq5bPZzHH-kTRKKVmv9NACXycjpziPsRTDzkuy-dVU"),i=(0,d.UU)(e,f),j={USERS:"users",USER_SETTINGS:"user_settings",DIET_RECORDS:"diet_records",INGREDIENTS:"ingredients",DISHES:"dishes"}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73147:(a,b,c)=>{"use strict";c.d(b,{K$:()=>f});var d=c(94612);class e{constructor(a){this.config=a}buildApiUrl(a){let b=this.config.baseURL.replace(/\/$/,"");return b.includes("gateway.988886.xyz")?`${b}${a}`:this.config.authCode&&this.config.authCode.trim()?`${b}/${this.config.authCode}${a}`:`${b}${a}`}async getAvailableModels(){try{let a=this.buildApiUrl("/v1/models");console.log("请求URL:",a);let b=await d.A.get(a,{headers:{Authorization:`Bearer ${this.config.apiKey}`,"Content-Type":"application/json"}});if(console.log("API响应:",b.data),b.data&&b.data.data)return b.data.data.map(a=>a.id);return[]}catch(a){return console.error("获取模型列表失败:",a),[]}}async analyzeDietText(a){let b=`
请分析以下饮食描述，提取出具体的食材、数量和菜品信息。请严格按照JSON格式返回，不要包含任何其他文字：

用户输入：${a}

请返回JSON格式：
{
  "ingredients": [
    {
      "name": "食材名称",
      "amount": 数量(数字),
      "unit": "单位(g/ml/个等)"
    }
  ],
  "dishes": [
    {
      "name": "菜品名称",
      "category": "dish|staple|drink|side"
    }
  ],
  "meal_type": "breakfast|lunch|dinner|snack",
  "estimated_time": "YYYY-MM-DD HH:mm:ss"
}

注意：
1. 根据当前北京时间判断餐次
2. 尽量准确估算食材重量
3. 菜品分类：dish(菜品)、staple(主食)、drink(饮料)、side(配菜)
4. 只返回JSON，不要其他解释
`;try{let a=(await d.A.post(this.buildApiUrl("/v1/chat/completions"),{model:this.config.model,messages:[{role:"user",content:b}],temperature:.3,max_tokens:1e3},{headers:{Authorization:`Bearer ${this.config.apiKey}`,"Content-Type":"application/json"}})).data.choices[0].message.content;return JSON.parse(a)}catch(a){throw console.error("AI分析失败:",a),Error("AI分析失败，请稍后重试")}}async analyzeDietImage(a,b){let c=`
请分析这张食物图片，识别出具体的食材、数量和菜品信息。${b?`用户补充说明：${b}`:""}

请严格按照JSON格式返回，不要包含任何其他文字：

{
  "ingredients": [
    {
      "name": "食材名称",
      "amount": 数量(数字),
      "unit": "单位(g/ml/个等)"
    }
  ],
  "dishes": [
    {
      "name": "菜品名称",
      "category": "dish|staple|drink|side"
    }
  ],
  "meal_type": "breakfast|lunch|dinner|snack",
  "estimated_time": "YYYY-MM-DD HH:mm:ss"
}

注意：
1. 根据当前北京时间判断餐次
2. 根据图片中食物的视觉大小估算重量
3. 菜品分类：dish(菜品)、staple(主食)、drink(饮料)、side(配菜)
4. 只返回JSON，不要其他解释
`;try{let b=(await d.A.post(this.buildApiUrl("/v1/chat/completions"),{model:this.config.model,messages:[{role:"user",content:[{type:"text",text:c},{type:"image_url",image_url:{url:a}}]}],temperature:.3,max_tokens:1e3},{headers:{Authorization:`Bearer ${this.config.apiKey}`,"Content-Type":"application/json"}})).data.choices[0].message.content;return JSON.parse(b)}catch(a){throw console.error("AI图片分析失败:",a),Error("AI图片分析失败，请稍后重试")}}async analyzeCalories(a){let b=a.map(a=>`${a.name} ${a.amount}${a.unit}`).join("、"),c=`
请分析以下食材的热量和营养信息：

食材清单：${b}

请严格按照JSON格式返回，不要包含任何其他文字：

{
  "total_calories": 总热量(数字),
  "analysis": "简短的营养分析和建议(50字以内)",
  "breakdown": [
    {
      "ingredient": "食材名称",
      "calories": 该食材热量(数字)
    }
  ]
}

注意：
1. 热量计算要准确，基于实际营养数据
2. 分析要简洁实用
3. 只返回JSON，不要其他解释
`;try{let a=(await d.A.post(this.buildApiUrl("/v1/chat/completions"),{model:this.config.model,messages:[{role:"user",content:c}],temperature:.3,max_tokens:800},{headers:{Authorization:`Bearer ${this.config.apiKey}`,"Content-Type":"application/json"}})).data.choices[0].message.content;return JSON.parse(a)}catch(a){throw console.error("热量分析失败:",a),Error("热量分析失败，请稍后重试")}}}function f(a){return new e(a)}},74075:a=>{"use strict";a.exports=require("zlib")},77988:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{POST:()=>k});var e=c(32190),f=c(50639),g=c(14250),h=c(73147),i=c(6710),j=a([i]);i=(j.then?(await j)():j)[0];let l=f.Ik({input:f.Yj().min(1,"输入内容不能为空"),inputType:f.k5(["text","image"]),imageUrl:f.Yj().optional()});async function k(a){try{let b,c=a.headers.get("authorization");if(!c)return e.NextResponse.json({success:!1,error:"未授权访问"},{status:401});let d=JSON.parse(c.replace("Bearer ","")),f=await a.json(),{input:g,inputType:j,imageUrl:k}=l.parse(f),m=await i.AM.getUserSettings(d.id);if(!m||!m.api_key)return e.NextResponse.json({success:!1,error:"请先配置AI模型设置"},{status:400});let n=(0,h.K$)({baseURL:m.api_base_url,apiKey:m.api_key,authCode:d.auth_code,model:m.ai_model});return b="image"===j&&k?await n.analyzeDietImage(k,g):await n.analyzeDietText(g),e.NextResponse.json({success:!0,data:b})}catch(b){if(console.error("饮食分析错误:",b),b instanceof g.G)return e.NextResponse.json({success:!1,error:b.issues[0].message},{status:400});let a=b instanceof Error?b.message:"饮食分析失败";return e.NextResponse.json({success:!1,error:a},{status:500})}}d()}catch(a){d(a)}})},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},93139:a=>{"use strict";a.exports=import("bcryptjs")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[96],()=>b(b.s=28505));module.exports=c})();
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/store/useAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { User } from '@/types';\n\ninterface AuthState {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (user: User) => void;\n  logout: () => void;\n  setLoading: (loading: boolean) => void;\n  updateUser: (user: Partial<User>) => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      \n      login: (user: User) => {\n        set({ user, isAuthenticated: true, isLoading: false });\n      },\n      \n      logout: () => {\n        set({ user: null, isAuthenticated: false, isLoading: false });\n      },\n      \n      setLoading: (loading: boolean) => {\n        set({ isLoading: loading });\n      },\n      \n      updateUser: (userData: Partial<User>) => {\n        const currentUser = get().user;\n        if (currentUser) {\n          set({ user: { ...currentUser, ...userData } });\n        }\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAaO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,iBAAiB;QACjB,WAAW;QAEX,OAAO,CAAC;YACN,IAAI;gBAAE;gBAAM,iBAAiB;gBAAM,WAAW;YAAM;QACtD;QAEA,QAAQ;YACN,IAAI;gBAAE,MAAM;gBAAM,iBAAiB;gBAAO,WAAW;YAAM;QAC7D;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,YAAY,CAAC;YACX,MAAM,cAAc,MAAM,IAAI;YAC9B,IAAI,aAAa;gBACf,IAAI;oBAAE,MAAM;wBAAE,GAAG,WAAW;wBAA<PERSON>,GAAG,QAAQ;oBAAC;gBAAE;YAC9C;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/auth/AuthGuard.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/useAuthStore';\n\ninterface AuthGuardProps {\n  children: React.ReactNode;\n  requireAuth?: boolean;\n  requireAdmin?: boolean;\n  redirectTo?: string;\n}\n\nexport function AuthGuard({ \n  children, \n  requireAuth = true, \n  requireAdmin = false,\n  redirectTo \n}: AuthGuardProps) {\n  const { isAuthenticated, user, isLoading } = useAuthStore();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (isLoading) return;\n\n    if (requireAuth && !isAuthenticated) {\n      router.push(redirectTo || '/login');\n      return;\n    }\n\n    if (requireAdmin && user?.role !== 'admin') {\n      router.push('/dashboard');\n      return;\n    }\n\n    if (!requireAuth && isAuthenticated) {\n      router.push('/dashboard');\n      return;\n    }\n  }, [isAuthenticated, user, isLoading, requireAuth, requireAdmin, router, redirectTo]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900\"></div>\n      </div>\n    );\n  }\n\n  if (requireAuth && !isAuthenticated) {\n    return null;\n  }\n\n  if (requireAdmin && user?.role !== 'admin') {\n    return null;\n  }\n\n  if (!requireAuth && isAuthenticated) {\n    return null;\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAaO,SAAS,UAAU,KAKT;QALS,EACxB,QAAQ,EACR,cAAc,IAAI,EAClB,eAAe,KAAK,EACpB,UAAU,EACK,GALS;;IAMxB,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IACxD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,WAAW;YAEf,IAAI,eAAe,CAAC,iBAAiB;gBACnC,OAAO,IAAI,CAAC,cAAc;gBAC1B;YACF;YAEA,IAAI,gBAAgB,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,SAAS;gBAC1C,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,CAAC,eAAe,iBAAiB;gBACnC,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;8BAAG;QAAC;QAAiB;QAAM;QAAW;QAAa;QAAc;QAAQ;KAAW;IAEpF,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,eAAe,CAAC,iBAAiB;QACnC,OAAO;IACT;IAEA,IAAI,gBAAgB,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,SAAS;QAC1C,OAAO;IACT;IAEA,IAAI,CAAC,eAAe,iBAAiB;QACnC,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GAjDgB;;QAM+B,+HAAA,CAAA,eAAY;QAC1C,qIAAA,CAAA,YAAS;;;KAPV", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Switch({\n  className,\n  ...props\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\n  return (\n    <SwitchPrimitive.Root\n      data-slot=\"switch\"\n      className={cn(\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <SwitchPrimitive.Thumb\n        data-slot=\"switch-thumb\"\n        className={cn(\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\n        )}\n      />\n    </SwitchPrimitive.Root>\n  )\n}\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,KAGoC;QAHpC,EACd,SAAS,EACT,GAAG,OAC+C,GAHpC;IAId,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,qKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV;KArBS", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 681, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACb,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,KAGY;QAHZ,EACpB,SAAS,EACT,GAAG,OAC6B,GAHZ;IAIpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/admin/AdminDashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { toast } from 'sonner';\nimport axios from 'axios';\nimport { format } from 'date-fns';\nimport { zhCN } from 'date-fns/locale';\n\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Switch } from '@/components/ui/switch';\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\nimport { Plus, Trash2, Key, <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Settings } from 'lucide-react';\nimport { useAuthStore } from '@/store/useAuthStore';\nimport { User } from '@/types';\n\nconst createUserSchema = z.object({\n  username: z.string().min(3, '用户名至少3个字符').max(50, '用户名最多50个字符'),\n  email: z.string().optional(),\n  password: z.string().min(6, '密码至少6个字符'),\n  role: z.enum(['admin', 'user']),\n  auth_code: z.string().optional(),\n});\n\nconst editAuthCodeSchema = z.object({\n  auth_code: z.string().optional(),\n});\n\nconst resetPasswordSchema = z.object({\n  newPassword: z.string().min(6, '密码至少6个字符'),\n});\n\ntype CreateUserFormData = z.infer<typeof createUserSchema>;\ntype ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;\ntype EditAuthCodeFormData = z.infer<typeof editAuthCodeSchema>;\n\nexport function AdminDashboard() {\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [resetPasswordDialogOpen, setResetPasswordDialogOpen] = useState(false);\n  const [editAuthCodeDialogOpen, setEditAuthCodeDialogOpen] = useState(false);\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\n  const [creating, setCreating] = useState(false);\n  const [resetting, setResetting] = useState(false);\n  const [editingAuthCode, setEditingAuthCode] = useState(false);\n  const [registrationEnabled, setRegistrationEnabled] = useState(true);\n  const [updatingSettings, setUpdatingSettings] = useState(false);\n  \n  const { user } = useAuthStore();\n\n  const {\n    register: registerCreate,\n    handleSubmit: handleCreateSubmit,\n    formState: { errors: createErrors },\n    reset: resetCreateForm,\n  } = useForm<CreateUserFormData>({\n    resolver: zodResolver(createUserSchema),\n    defaultValues: {\n      role: 'user',\n    },\n  });\n\n  const {\n    register: registerReset,\n    handleSubmit: handleResetSubmit,\n    formState: { errors: resetErrors },\n    reset: resetPasswordForm,\n  } = useForm<ResetPasswordFormData>({\n    resolver: zodResolver(resetPasswordSchema),\n  });\n\n  const {\n    register: registerAuthCode,\n    handleSubmit: handleAuthCodeSubmit,\n    formState: { errors: authCodeErrors },\n    reset: resetAuthCodeForm,\n    setValue: setAuthCodeValue,\n  } = useForm<EditAuthCodeFormData>({\n    resolver: zodResolver(editAuthCodeSchema),\n  });\n\n  // 获取系统设置\n  const fetchSystemSettings = async () => {\n    if (!user) return;\n\n    try {\n      const response = await axios.get('/api/admin/settings', {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n        },\n      });\n\n      if (response.data.success) {\n        setRegistrationEnabled(response.data.data.registration_enabled);\n      }\n    } catch (error: unknown) {\n      console.error('获取系统设置错误:', error);\n    }\n  };\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    if (!user) return;\n\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/admin/users', {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n        },\n      });\n\n      if (response.data.success) {\n        setUsers(response.data.data);\n      } else {\n        toast.error('获取用户列表失败');\n      }\n    } catch (error: unknown) {\n      console.error('获取用户列表错误:', error);\n      const axiosError = error as { response?: { data?: { error?: string } } };\n      toast.error(axiosError.response?.data?.error || '获取用户列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 创建用户\n  const onCreateUser = async (data: CreateUserFormData) => {\n    if (!user) return;\n\n    try {\n      setCreating(true);\n      const response = await axios.post('/api/admin/users', data, {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (response.data.success) {\n        toast.success('用户创建成功');\n        setUsers([response.data.data, ...users]);\n        setCreateDialogOpen(false);\n        resetCreateForm();\n      } else {\n        toast.error(response.data.error || '创建失败');\n      }\n    } catch (error: unknown) {\n      console.error('创建用户错误:', error);\n      const axiosError = error as { response?: { data?: { error?: string } } };\n      toast.error(axiosError.response?.data?.error || '创建失败');\n    } finally {\n      setCreating(false);\n    }\n  };\n\n  // 重置密码\n  const onResetPassword = async (data: ResetPasswordFormData) => {\n    if (!selectedUser || !user) return;\n\n    try {\n      setResetting(true);\n      const response = await axios.patch(`/api/admin/users/${selectedUser.id}`, {\n        newPassword: data.newPassword,\n      }, {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (response.data.success) {\n        toast.success('密码重置成功');\n        setResetPasswordDialogOpen(false);\n        setSelectedUser(null);\n        resetPasswordForm();\n      } else {\n        toast.error(response.data.error || '重置失败');\n      }\n    } catch (error: unknown) {\n      console.error('重置密码错误:', error);\n      const axiosError = error as { response?: { data?: { error?: string } } };\n      toast.error(axiosError.response?.data?.error || '重置失败');\n    } finally {\n      setResetting(false);\n    }\n  };\n\n  // 删除用户\n  const deleteUser = async (userId: string) => {\n    if (!user) return;\n\n    if (!confirm('确定要删除这个用户吗？此操作无法撤销。')) {\n      return;\n    }\n\n    try {\n      const response = await axios.delete(`/api/admin/users/${userId}`, {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n        },\n      });\n\n      if (response.data.success) {\n        toast.success('用户删除成功');\n        setUsers(users.filter(u => u.id !== userId));\n      } else {\n        toast.error('删除失败');\n      }\n    } catch (error: unknown) {\n      console.error('删除用户错误:', error);\n      const axiosError = error as { response?: { data?: { error?: string } } };\n      toast.error(axiosError.response?.data?.error || '删除失败');\n    }\n  };\n\n  // 更新系统设置\n  const updateSystemSettings = async (enabled: boolean) => {\n    if (!user) return;\n\n    try {\n      setUpdatingSettings(true);\n      const response = await axios.put('/api/admin/settings', {\n        registration_enabled: enabled,\n      }, {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (response.data.success) {\n        setRegistrationEnabled(enabled);\n        toast.success('系统设置更新成功');\n      } else {\n        toast.error(response.data.error || '更新失败');\n      }\n    } catch (error: unknown) {\n      console.error('更新系统设置错误:', error);\n      const axiosError = error as { response?: { data?: { error?: string } } };\n      toast.error(axiosError.response?.data?.error || '更新失败');\n    } finally {\n      setUpdatingSettings(false);\n    }\n  };\n\n  // 编辑授权码\n  const onEditAuthCode = async (data: EditAuthCodeFormData) => {\n    if (!selectedUser || !user) return;\n\n    try {\n      setEditingAuthCode(true);\n      const response = await axios.put(`/api/admin/users/${selectedUser.id}`, {\n        auth_code: data.auth_code,\n      }, {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (response.data.success) {\n        toast.success('授权码更新成功');\n        setUsers(users.map(u =>\n          u.id === selectedUser.id\n            ? { ...u, auth_code: data.auth_code }\n            : u\n        ));\n        setEditAuthCodeDialogOpen(false);\n        setSelectedUser(null);\n        resetAuthCodeForm();\n      } else {\n        toast.error(response.data.error || '更新失败');\n      }\n    } catch (error: unknown) {\n      console.error('更新授权码错误:', error);\n      const axiosError = error as { response?: { data?: { error?: string } } };\n      toast.error(axiosError.response?.data?.error || '更新失败');\n    } finally {\n      setEditingAuthCode(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchUsers();\n    fetchSystemSettings();\n  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  if (loading) {\n    return (\n      <Card>\n        <CardContent className=\"flex items-center justify-center py-8\">\n          <Loader2 className=\"w-6 h-6 animate-spin mr-2\" />\n          加载中...\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold\">用户管理</h2>\n          <p className=\"text-gray-600\">管理系统用户和权限</p>\n        </div>\n        <div className=\"flex gap-2\">\n          <Button\n            variant=\"outline\"\n            onClick={fetchUsers}\n            disabled={loading}\n          >\n            <RefreshCw className=\"w-4 h-4 mr-2\" />\n            刷新\n          </Button>\n          <Button onClick={() => setCreateDialogOpen(true)}>\n            <Plus className=\"w-4 h-4 mr-2\" />\n            创建用户\n          </Button>\n        </div>\n      </div>\n\n      {/* 系统设置 */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Settings className=\"w-5 h-5\" />\n            系统设置\n          </CardTitle>\n          <CardDescription>\n            管理系统全局设置\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"registration-switch\">允许用户注册</Label>\n              <p className=\"text-sm text-gray-500\">\n                关闭后，新用户无法自行注册账号\n              </p>\n            </div>\n            <Switch\n              id=\"registration-switch\"\n              checked={registrationEnabled}\n              onCheckedChange={updateSystemSettings}\n              disabled={updatingSettings}\n            />\n          </div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardHeader>\n          <CardTitle>用户列表</CardTitle>\n          <CardDescription>\n            系统中的所有用户\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"overflow-x-auto\">\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead>用户名</TableHead>\n                  <TableHead className=\"hidden sm:table-cell\">邮箱</TableHead>\n                  <TableHead>角色</TableHead>\n                  <TableHead className=\"hidden md:table-cell\">授权码</TableHead>\n                  <TableHead className=\"hidden lg:table-cell\">创建时间</TableHead>\n                  <TableHead>操作</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {users.map((u) => (\n                  <TableRow key={u.id}>\n                    <TableCell className=\"font-medium\">{u.username}</TableCell>\n                    <TableCell className=\"hidden sm:table-cell\">{u.email}</TableCell>\n                    <TableCell>\n                      <span className={`px-2 py-1 rounded-full text-xs ${\n                        u.role === 'admin'\n                          ? 'bg-red-100 text-red-800'\n                          : 'bg-blue-100 text-blue-800'\n                      }`}>\n                        {u.role === 'admin' ? '管理员' : '用户'}\n                      </span>\n                    </TableCell>\n                    <TableCell className=\"hidden md:table-cell\">{u.auth_code || '-'}</TableCell>\n                    <TableCell className=\"hidden lg:table-cell\">\n                      {format(new Date(u.created_at), 'yyyy-MM-dd HH:mm', { locale: zhCN })}\n                    </TableCell>\n                    <TableCell>\n                      <div className=\"flex gap-1\">\n                        <Button\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          onClick={() => {\n                            setSelectedUser(u);\n                            setAuthCodeValue('auth_code', u.auth_code || '');\n                            setEditAuthCodeDialogOpen(true);\n                          }}\n                          title=\"编辑授权码\"\n                        >\n                          <Edit className=\"w-4 h-4\" />\n                        </Button>\n                        <Button\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          onClick={() => {\n                            setSelectedUser(u);\n                            setResetPasswordDialogOpen(true);\n                          }}\n                          title=\"重置密码\"\n                        >\n                          <Key className=\"w-4 h-4\" />\n                        </Button>\n                        {u.id !== user?.id && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => deleteUser(u.id)}\n                            title=\"删除用户\"\n                          >\n                            <Trash2 className=\"w-4 h-4 text-red-500\" />\n                          </Button>\n                        )}\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 创建用户对话框 */}\n      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>创建新用户</DialogTitle>\n            <DialogDescription>\n              填写用户信息创建新账号\n            </DialogDescription>\n          </DialogHeader>\n          <form onSubmit={handleCreateSubmit(onCreateUser)} className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"username\">用户名</Label>\n              <Input\n                id=\"username\"\n                {...registerCreate('username')}\n                disabled={creating}\n              />\n              {createErrors.username && (\n                <p className=\"text-sm text-red-500\">{createErrors.username.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">邮箱（可选）</Label>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                placeholder=\"可选填写\"\n                {...registerCreate('email')}\n                disabled={creating}\n              />\n              {createErrors.email && (\n                <p className=\"text-sm text-red-500\">{createErrors.email.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"password\">密码</Label>\n              <Input\n                id=\"password\"\n                type=\"password\"\n                {...registerCreate('password')}\n                disabled={creating}\n              />\n              {createErrors.password && (\n                <p className=\"text-sm text-red-500\">{createErrors.password.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"auth_code\">授权码（可选）</Label>\n              <Input\n                id=\"auth_code\"\n                {...registerCreate('auth_code')}\n                disabled={creating}\n              />\n            </div>\n\n            <DialogFooter>\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => setCreateDialogOpen(false)}\n                disabled={creating}\n              >\n                取消\n              </Button>\n              <Button type=\"submit\" disabled={creating}>\n                {creating ? (\n                  <>\n                    <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                    创建中...\n                  </>\n                ) : (\n                  '创建用户'\n                )}\n              </Button>\n            </DialogFooter>\n          </form>\n        </DialogContent>\n      </Dialog>\n\n      {/* 重置密码对话框 */}\n      <Dialog open={resetPasswordDialogOpen} onOpenChange={setResetPasswordDialogOpen}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>重置密码</DialogTitle>\n            <DialogDescription>\n              为用户 {selectedUser?.username} 设置新密码\n            </DialogDescription>\n          </DialogHeader>\n          <form onSubmit={handleResetSubmit(onResetPassword)} className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"newPassword\">新密码</Label>\n              <Input\n                id=\"newPassword\"\n                type=\"password\"\n                {...registerReset('newPassword')}\n                disabled={resetting}\n              />\n              {resetErrors.newPassword && (\n                <p className=\"text-sm text-red-500\">{resetErrors.newPassword.message}</p>\n              )}\n            </div>\n\n            <DialogFooter>\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => setResetPasswordDialogOpen(false)}\n                disabled={resetting}\n              >\n                取消\n              </Button>\n              <Button type=\"submit\" disabled={resetting}>\n                {resetting ? (\n                  <>\n                    <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                    重置中...\n                  </>\n                ) : (\n                  '重置密码'\n                )}\n              </Button>\n            </DialogFooter>\n          </form>\n        </DialogContent>\n      </Dialog>\n\n      {/* 编辑授权码对话框 */}\n      <Dialog open={editAuthCodeDialogOpen} onOpenChange={setEditAuthCodeDialogOpen}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>编辑授权码</DialogTitle>\n            <DialogDescription>\n              为用户 {selectedUser?.username} 设置授权码\n            </DialogDescription>\n          </DialogHeader>\n          <form onSubmit={handleAuthCodeSubmit(onEditAuthCode)} className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"auth_code\">授权码</Label>\n              <Input\n                id=\"auth_code\"\n                placeholder=\"留空表示不使用授权码\"\n                {...registerAuthCode('auth_code')}\n                disabled={editingAuthCode}\n              />\n              {authCodeErrors.auth_code && (\n                <p className=\"text-sm text-red-500\">{authCodeErrors.auth_code.message}</p>\n              )}\n              <p className=\"text-xs text-gray-500\">\n                授权码用于访问需要特殊权限的API服务\n              </p>\n            </div>\n\n            <DialogFooter>\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => setEditAuthCodeDialogOpen(false)}\n                disabled={editingAuthCode}\n              >\n                取消\n              </Button>\n              <Button type=\"submit\" disabled={editingAuthCode}>\n                {editingAuthCode ? (\n                  <>\n                    <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                    更新中...\n                  </>\n                ) : (\n                  '更新授权码'\n                )}\n              </Button>\n            </DialogFooter>\n          </form>\n        </DialogContent>\n      </Dialog>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AApBA;;;;;;;;;;;;;;;;;;AAuBA,MAAM,mBAAmB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,IAAI;IACjD,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,MAAM,gLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;KAAO;IAC9B,WAAW,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAChC;AAEA,MAAM,qBAAqB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,WAAW,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAChC;AAEA,MAAM,sBAAsB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACjC;AAMO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EACJ,UAAU,cAAc,EACxB,cAAc,kBAAkB,EAChC,WAAW,EAAE,QAAQ,YAAY,EAAE,EACnC,OAAO,eAAe,EACvB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAsB;QAC9B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM;QACR;IACF;IAEA,MAAM,EACJ,UAAU,aAAa,EACvB,cAAc,iBAAiB,EAC/B,WAAW,EAAE,QAAQ,WAAW,EAAE,EAClC,OAAO,iBAAiB,EACzB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAyB;QACjC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,EACJ,UAAU,gBAAgB,EAC1B,cAAc,oBAAoB,EAClC,WAAW,EAAE,QAAQ,cAAc,EAAE,EACrC,OAAO,iBAAiB,EACxB,UAAU,gBAAgB,EAC3B,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAwB;QAChC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,SAAS;IACT,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,uBAAuB;gBACtD,SAAS;oBACP,iBAAiB,AAAC,UAA8B,OAArB,KAAK,SAAS,CAAC;gBAC5C;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,uBAAuB,SAAS,IAAI,CAAC,IAAI,CAAC,oBAAoB;YAChE;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,SAAS;IACT,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,oBAAoB;gBACnD,SAAS;oBACP,iBAAiB,AAAC,UAA8B,OAArB,KAAK,SAAS,CAAC;gBAC5C;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,SAAS,SAAS,IAAI,CAAC,IAAI;YAC7B,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAgB;gBAGX,2BAAA;YAFZ,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,aAAa;YACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAA,uBAAA,WAAW,QAAQ,cAAnB,4CAAA,4BAAA,qBAAqB,IAAI,cAAzB,gDAAA,0BAA2B,KAAK,KAAI;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;IACP,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,YAAY;YACZ,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,oBAAoB,MAAM;gBAC1D,SAAS;oBACP,iBAAiB,AAAC,UAA8B,OAArB,KAAK,SAAS,CAAC;oBAC1C,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,SAAS;oBAAC,SAAS,IAAI,CAAC,IAAI;uBAAK;iBAAM;gBACvC,oBAAoB;gBACpB;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI;YACrC;QACF,EAAE,OAAO,OAAgB;gBAGX,2BAAA;YAFZ,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,aAAa;YACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAA,uBAAA,WAAW,QAAQ,cAAnB,4CAAA,4BAAA,qBAAqB,IAAI,cAAzB,gDAAA,0BAA2B,KAAK,KAAI;QAClD,SAAU;YACR,YAAY;QACd;IACF;IAEA,OAAO;IACP,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,gBAAgB,CAAC,MAAM;QAE5B,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,KAAK,CAAC,AAAC,oBAAmC,OAAhB,aAAa,EAAE,GAAI;gBACxE,aAAa,KAAK,WAAW;YAC/B,GAAG;gBACD,SAAS;oBACP,iBAAiB,AAAC,UAA8B,OAArB,KAAK,SAAS,CAAC;oBAC1C,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,2BAA2B;gBAC3B,gBAAgB;gBAChB;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI;YACrC;QACF,EAAE,OAAO,OAAgB;gBAGX,2BAAA;YAFZ,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,aAAa;YACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAA,uBAAA,WAAW,QAAQ,cAAnB,4CAAA,4BAAA,qBAAqB,IAAI,cAAzB,gDAAA,0BAA2B,KAAK,KAAI;QAClD,SAAU;YACR,aAAa;QACf;IACF;IAEA,OAAO;IACP,MAAM,aAAa,OAAO;QACxB,IAAI,CAAC,MAAM;QAEX,IAAI,CAAC,QAAQ,wBAAwB;YACnC;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,AAAC,oBAA0B,OAAP,SAAU;gBAChE,SAAS;oBACP,iBAAiB,AAAC,UAA8B,OAArB,KAAK,SAAS,CAAC;gBAC5C;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,SAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACtC,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAgB;gBAGX,2BAAA;YAFZ,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,aAAa;YACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAA,uBAAA,WAAW,QAAQ,cAAnB,4CAAA,4BAAA,qBAAqB,IAAI,cAAzB,gDAAA,0BAA2B,KAAK,KAAI;QAClD;IACF;IAEA,SAAS;IACT,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,oBAAoB;YACpB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,uBAAuB;gBACtD,sBAAsB;YACxB,GAAG;gBACD,SAAS;oBACP,iBAAiB,AAAC,UAA8B,OAArB,KAAK,SAAS,CAAC;oBAC1C,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,uBAAuB;gBACvB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI;YACrC;QACF,EAAE,OAAO,OAAgB;gBAGX,2BAAA;YAFZ,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,aAAa;YACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAA,uBAAA,WAAW,QAAQ,cAAnB,4CAAA,4BAAA,qBAAqB,IAAI,cAAzB,gDAAA,0BAA2B,KAAK,KAAI;QAClD,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,QAAQ;IACR,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,gBAAgB,CAAC,MAAM;QAE5B,IAAI;YACF,mBAAmB;YACnB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,AAAC,oBAAmC,OAAhB,aAAa,EAAE,GAAI;gBACtE,WAAW,KAAK,SAAS;YAC3B,GAAG;gBACD,SAAS;oBACP,iBAAiB,AAAC,UAA8B,OAArB,KAAK,SAAS,CAAC;oBAC1C,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,SAAS,MAAM,GAAG,CAAC,CAAA,IACjB,EAAE,EAAE,KAAK,aAAa,EAAE,GACpB;wBAAE,GAAG,CAAC;wBAAE,WAAW,KAAK,SAAS;oBAAC,IAClC;gBAEN,0BAA0B;gBAC1B,gBAAgB;gBAChB;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI;YACrC;QACF,EAAE,OAAO,OAAgB;gBAGX,2BAAA;YAFZ,QAAQ,KAAK,CAAC,YAAY;YAC1B,MAAM,aAAa;YACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAA,uBAAA,WAAW,QAAQ,cAAnB,4CAAA,4BAAA,qBAAqB,IAAI,cAAzB,gDAAA,0BAA2B,KAAK,KAAI;QAClD,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;YACA;QACF;mCAAG;QAAC;KAAK,GAAG,kDAAkD;IAE9D,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAA8B;;;;;;;;;;;;IAKzD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,oBAAoB;;kDACzC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGlC,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAsB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,6LAAC,qIAAA,CAAA,SAAM;oCACL,IAAG;oCACH,SAAS;oCACT,iBAAiB;oCACjB,UAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMlB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kDACJ,6LAAC,oIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8DACP,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAuB;;;;;;8DAC5C,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAuB;;;;;;8DAC5C,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAuB;;;;;;8DAC5C,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;;;;;;kDAGf,6LAAC,oIAAA,CAAA,YAAS;kDACP,MAAM,GAAG,CAAC,CAAC,kBACV,6LAAC,oIAAA,CAAA,WAAQ;;kEACP,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAe,EAAE,QAAQ;;;;;;kEAC9C,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAwB,EAAE,KAAK;;;;;;kEACpD,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAK,WAAW,AAAC,kCAIjB,OAHC,EAAE,IAAI,KAAK,UACP,4BACA;sEAEH,EAAE,IAAI,KAAK,UAAU,QAAQ;;;;;;;;;;;kEAGlC,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAwB,EAAE,SAAS,IAAI;;;;;;kEAC5D,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,EAAE,UAAU,GAAG,oBAAoB;4DAAE,QAAQ,oJAAA,CAAA,OAAI;wDAAC;;;;;;kEAErE,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;wEACP,gBAAgB;wEAChB,iBAAiB,aAAa,EAAE,SAAS,IAAI;wEAC7C,0BAA0B;oEAC5B;oEACA,OAAM;8EAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;wEACP,gBAAgB;wEAChB,2BAA2B;oEAC7B;oEACA,OAAM;8EAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;gEAEhB,EAAE,EAAE,MAAK,iBAAA,2BAAA,KAAM,EAAE,mBAChB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,WAAW,EAAE,EAAE;oEAC9B,OAAM;8EAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CAhDb,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA8D/B,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;;sCACZ,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,6LAAC;4BAAK,UAAU,mBAAmB;4BAAe,WAAU;;8CAC1D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACF,GAAG,eAAe,WAAW;4CAC9B,UAAU;;;;;;wCAEX,aAAa,QAAQ,kBACpB,6LAAC;4CAAE,WAAU;sDAAwB,aAAa,QAAQ,CAAC,OAAO;;;;;;;;;;;;8CAItE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,aAAY;4CACX,GAAG,eAAe,QAAQ;4CAC3B,UAAU;;;;;;wCAEX,aAAa,KAAK,kBACjB,6LAAC;4CAAE,WAAU;sDAAwB,aAAa,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAInE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACJ,GAAG,eAAe,WAAW;4CAC9B,UAAU;;;;;;wCAEX,aAAa,QAAQ,kBACpB,6LAAC;4CAAE,WAAU;sDAAwB,aAAa,QAAQ,CAAC,OAAO;;;;;;;;;;;;8CAItE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACF,GAAG,eAAe,YAAY;4CAC/B,UAAU;;;;;;;;;;;;8CAId,6LAAC,qIAAA,CAAA,eAAY;;sDACX,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,oBAAoB;4CACnC,UAAU;sDACX;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,UAAU;sDAC7B,yBACC;;kEACE,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;+DAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASZ,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAyB,cAAc;0BACnD,cAAA,6LAAC,qIAAA,CAAA,gBAAa;;sCACZ,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;;wCAAC;wCACZ,yBAAA,mCAAA,aAAc,QAAQ;wCAAC;;;;;;;;;;;;;sCAGhC,6LAAC;4BAAK,UAAU,kBAAkB;4BAAkB,WAAU;;8CAC5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACJ,GAAG,cAAc,cAAc;4CAChC,UAAU;;;;;;wCAEX,YAAY,WAAW,kBACtB,6LAAC;4CAAE,WAAU;sDAAwB,YAAY,WAAW,CAAC,OAAO;;;;;;;;;;;;8CAIxE,6LAAC,qIAAA,CAAA,eAAY;;sDACX,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,2BAA2B;4CAC1C,UAAU;sDACX;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,UAAU;sDAC7B,0BACC;;kEACE,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;+DAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASZ,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAwB,cAAc;0BAClD,cAAA,6LAAC,qIAAA,CAAA,gBAAa;;sCACZ,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;;wCAAC;wCACZ,yBAAA,mCAAA,aAAc,QAAQ;wCAAC;;;;;;;;;;;;;sCAGhC,6LAAC;4BAAK,UAAU,qBAAqB;4BAAiB,WAAU;;8CAC9D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACX,GAAG,iBAAiB,YAAY;4CACjC,UAAU;;;;;;wCAEX,eAAe,SAAS,kBACvB,6LAAC;4CAAE,WAAU;sDAAwB,eAAe,SAAS,CAAC,OAAO;;;;;;sDAEvE,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,6LAAC,qIAAA,CAAA,eAAY;;sDACX,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,0BAA0B;4CACzC,UAAU;sDACX;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,UAAU;sDAC7B,gCACC;;kEACE,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;+DAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GAnkBgB;;QAaG,+HAAA,CAAA,eAAY;QAOzB,iKAAA,CAAA,UAAO;QAYP,iKAAA,CAAA,UAAO;QAUP,iKAAA,CAAA,UAAO;;;KA1CG", "debugId": null}}]}
# 饮食热量分析系统

一个现代化的多用户饮食热量分析和记录系统，支持文字和图片输入，使用AI进行智能分析。

## 功能特性

### 🍽️ 饮食分析与记录
- **多模态输入**：支持文字描述和图片上传
- **智能识别**：AI自动提取食材、数量和菜品信息
- **结果确认**：可编辑和修改AI分析结果
- **时间管理**：自动判断餐次（早中晚餐、加餐）

### 📊 热量分析
- **精准计算**：基于食材和数量计算总热量
- **营养点评**：AI提供简短的营养分析和建议
- **详细分解**：显示每种食材的热量贡献

### 📱 用户体验
- **响应式设计**：完美适配PC端和移动端
- **现代化界面**：使用Tailwind CSS和shadcn/ui组件
- **实时反馈**：即时的操作反馈和状态提示

### 👥 用户管理
- **多用户支持**：独立的用户账号系统
- **权限管理**：管理员和普通用户角色
- **设置配置**：个性化的AI模型和API配置

### 📈 历史记录
- **时间轴展示**：按时间顺序查看饮食记录
- **详细信息**：包含原始输入、食材、菜品和热量分析
- **记录管理**：支持删除不需要的记录

## 技术架构

### 前端技术栈
- **Next.js 14**：React全栈框架，支持SSR/SSG
- **TypeScript**：类型安全的JavaScript
- **Tailwind CSS**：实用优先的CSS框架
- **shadcn/ui**：现代化的React组件库
- **Zustand**：轻量级状态管理
- **React Hook Form + Zod**：表单处理和验证

### 后端技术栈
- **Next.js API Routes**：服务端API
- **Supabase**：PostgreSQL数据库和认证
- **bcryptjs**：密码加密
- **OpenAI兼容API**：AI模型集成

## 快速开始

### 环境要求
- Node.js 18.18.0+
- npm 或 yarn

### 安装步骤

1. **安装依赖**
   ```bash
   npm install
   ```

2. **配置环境变量**

   修改 `.env.local` 文件并配置以下变量：
   ```env
   # 数据库配置
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

   # 认证配置
   NEXTAUTH_URL=http://localhost:3985
   NEXTAUTH_SECRET=your_nextauth_secret

   # 管理员配置
   ADMIN_USERNAME=wrhsd
   ADMIN_PASSWORD=a123456

   # AI模型配置
   DEFAULT_AI_BASE_URL=https://api.openai.com
   DEFAULT_AI_API_KEY=your_openai_api_key

   # 图片上传配置
   IMAGE_UPLOAD_URL=https://xrea.988886.xyz/webdav/fixed-upload-new.php
   IMAGE_UPLOAD_PATH=l-weight
   ```

3. **设置数据库**

   在Supabase中执行 `database/schema.sql` 文件中的SQL语句来创建数据库表结构。

4. **启动开发服务器**
   ```bash
   npm run dev
   ```

   应用将在 `http://0.0.0.0:3985` 启动。

### 生产部署

1. **构建应用**
   ```bash
   npm run build
   ```

2. **启动生产服务器**
   ```bash
   npm start
   ```

## 使用指南

### 管理员功能
1. 使用默认管理员账号登录（用户名：wrhsd，密码：a123456）
2. 访问管理员页面进行用户管理
3. 可以创建新用户、重置密码、绑定授权码

### 用户功能
1. 注册新账号或使用管理员创建的账号登录
2. 在设置页面配置AI模型和API密钥
3. 开始记录饮食：
   - 输入文字描述或上传食物图片
   - 确认和修改AI分析结果
   - 保存记录到数据库
4. 查看历史记录和热量分析

### AI模型配置
系统支持OpenAI兼容的API，包括：
- OpenAI官方API
- 其他兼容OpenAI格式的API服务
- 支持授权码模式的API服务

## 数据库结构

### 主要表结构
- `users`：用户信息表
- `user_settings`：用户设置表
- `diet_records`：饮食记录表
- `ingredients`：食材记录表
- `dishes`：菜品库表

详细的数据库结构请参考 `database/schema.sql` 文件。

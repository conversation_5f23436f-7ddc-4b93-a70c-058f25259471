-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    auth_code VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('Asia/Shanghai', NOW()),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('Asia/Shanghai', NOW())
);

-- 用户设置表
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    ai_model VARCHAR(100) DEFAULT 'gpt-4o',
    api_base_url VARCHAR(255) DEFAULT 'https://api.openai.com',
    api_key VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('Asia/Shanghai', NOW()),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('Asia/Shanghai', NOW()),
    UNIQUE(user_id)
);

-- 菜品库表
CREATE TABLE IF NOT EXISTS dishes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    category VARCHAR(20) CHECK (category IN ('dish', 'staple', 'drink', 'side')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('Asia/Shanghai', NOW()),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('Asia/Shanghai', NOW()),
    UNIQUE(name, category)
);

-- 饮食记录表
CREATE TABLE IF NOT EXISTS diet_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    original_input TEXT NOT NULL,
    input_type VARCHAR(10) CHECK (input_type IN ('text', 'image')),
    image_url VARCHAR(500),
    meal_type VARCHAR(20) CHECK (meal_type IN ('breakfast', 'lunch', 'dinner', 'snack')),
    record_time TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('Asia/Shanghai', NOW()),
    total_calories DECIMAL(8,2) DEFAULT 0,
    ai_analysis TEXT,
    dishes JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('Asia/Shanghai', NOW()),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('Asia/Shanghai', NOW())
);

-- 食材记录表
CREATE TABLE IF NOT EXISTS ingredients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    diet_record_id UUID REFERENCES diet_records(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    amount DECIMAL(8,2) NOT NULL,
    unit VARCHAR(20) DEFAULT 'g',
    calories_per_100g DECIMAL(8,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('Asia/Shanghai', NOW())
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_diet_records_user_id ON diet_records(user_id);
CREATE INDEX IF NOT EXISTS idx_diet_records_record_time ON diet_records(record_time);
CREATE INDEX IF NOT EXISTS idx_diet_records_meal_type ON diet_records(meal_type);
CREATE INDEX IF NOT EXISTS idx_ingredients_diet_record_id ON ingredients(diet_record_id);
CREATE INDEX IF NOT EXISTS idx_dishes_name ON dishes(name);
CREATE INDEX IF NOT EXISTS idx_dishes_category ON dishes(category);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('Asia/Shanghai', NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_settings_updated_at BEFORE UPDATE ON user_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_diet_records_updated_at BEFORE UPDATE ON diet_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_dishes_updated_at BEFORE UPDATE ON dishes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认管理员用户（密码需要在应用中哈希处理）
-- 注意：这里的密码哈希需要在应用启动时处理
INSERT INTO users (username, email, password_hash, role) 
VALUES ('wrhsd', '<EMAIL>', 'placeholder_hash', 'admin')
ON CONFLICT (username) DO NOTHING;

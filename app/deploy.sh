#!/bin/bash

# 饮食热量分析系统部署脚本
# 使用方法: ./deploy.sh [环境]
# 环境选项: development, production

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-development}

if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "production" ]]; then
    log_error "无效的环境参数。使用: development 或 production"
    exit 1
fi

log_info "开始部署到 $ENVIRONMENT 环境..."

# 检查必要的文件
if [[ ! -f ".env.local" ]]; then
    log_error ".env.local 文件不存在，请先配置环境变量"
    exit 1
fi

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    log_error "Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    log_error "Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 停止现有容器
log_info "停止现有容器..."
docker-compose down || true

# 清理旧镜像（可选）
if [[ "$ENVIRONMENT" == "production" ]]; then
    log_info "清理旧镜像..."
    docker system prune -f || true
fi

# 构建镜像
log_info "构建应用镜像..."
if [[ "$ENVIRONMENT" == "production" ]]; then
    docker-compose build --no-cache
else
    docker-compose build
fi

# 启动服务
log_info "启动服务..."
if [[ "$ENVIRONMENT" == "production" ]]; then
    docker-compose up -d
else
    docker-compose up -d app redis
fi

# 等待服务启动
log_info "等待服务启动..."
sleep 10

# 健康检查
log_info "执行健康检查..."
max_attempts=30
attempt=1

while [[ $attempt -le $max_attempts ]]; do
    if curl -f http://localhost:3985/api/health &> /dev/null; then
        log_info "应用启动成功！"
        break
    fi
    
    if [[ $attempt -eq $max_attempts ]]; then
        log_error "应用启动失败，请检查日志"
        docker-compose logs app
        exit 1
    fi
    
    log_info "等待应用启动... ($attempt/$max_attempts)"
    sleep 5
    ((attempt++))
done

# 显示运行状态
log_info "服务状态:"
docker-compose ps

# 显示访问信息
log_info "部署完成！"
log_info "应用访问地址: http://localhost:3985"
log_info "管理员账号: wrhsd / a123456"

if [[ "$ENVIRONMENT" == "production" ]]; then
    log_info "生产环境部署完成，请确保:"
    log_info "1. 配置了正确的SSL证书"
    log_info "2. 设置了防火墙规则"
    log_info "3. 配置了监控和日志收集"
    log_info "4. 定期备份数据库"
fi

# 显示有用的命令
log_info "常用命令:"
log_info "查看日志: docker-compose logs -f app"
log_info "重启服务: docker-compose restart app"
log_info "停止服务: docker-compose down"
log_info "更新应用: ./deploy.sh $ENVIRONMENT"

# Docker Compose配置文件
# 用于本地开发和生产部署

version: '3.8'

services:
  # Next.js应用
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3985:3985"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - ADMIN_USERNAME=${ADMIN_USERNAME}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD}
      - DEFAULT_AI_BASE_URL=${DEFAULT_AI_BASE_URL}
      - DEFAULT_AI_API_KEY=${DEFAULT_AI_API_KEY}
      - IMAGE_UPLOAD_URL=${IMAGE_UPLOAD_URL}
      - IMAGE_UPLOAD_PATH=${IMAGE_UPLOAD_PATH}
    volumes:
      - ./.env.local:/app/.env.local:ro
    restart: unless-stopped
    depends_on:
      - redis
    networks:
      - app-network

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - app-network

  # Nginx反向代理（生产环境）
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - app-network

volumes:
  redis_data:

networks:
  app-network:
    driver: bridge

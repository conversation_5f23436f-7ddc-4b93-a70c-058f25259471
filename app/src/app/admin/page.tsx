import { AuthGuard } from '@/components/auth/AuthGuard';
import { AdminDashboard } from '@/components/admin/AdminDashboard';

export default function AdminPage() {
  return (
    <AuthGuard requireAuth={true} requireAdmin={true}>
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <h1 className="text-xl font-semibold text-gray-900">
                  管理员控制台
                </h1>
              </div>
              
              <div className="flex items-center space-x-4">
                <a
                  href="/dashboard"
                  className="text-sm text-gray-600 hover:text-gray-900"
                >
                  返回主页
                </a>
              </div>
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <AdminDashboard />
        </main>
      </div>
    </AuthGuard>
  );
}

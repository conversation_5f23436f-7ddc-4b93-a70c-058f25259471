import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { SystemSettingsService } from '@/lib/database';

// 验证管理员权限的中间件函数
async function verifyAdmin(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader) {
    return null;
  }

  try {
    const userInfo = JSON.parse(authHeader.replace('Bearer ', ''));
    if (userInfo.role !== 'admin') {
      return null;
    }
    return userInfo;
  } catch {
    return null;
  }
}

const updateSettingsSchema = z.object({
  registration_enabled: z.boolean().optional(),
});

// 获取系统设置
export async function GET(request: NextRequest) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, error: '权限不足' },
        { status: 403 }
      );
    }

    const settings = await SystemSettingsService.getAllSettings();

    return NextResponse.json({
      success: true,
      data: {
        registration_enabled: settings.registration_enabled === 'true',
      },
    });

  } catch (error) {
    console.error('获取系统设置错误:', error);
    return NextResponse.json(
      { success: false, error: '获取系统设置失败' },
      { status: 500 }
    );
  }
}

// 更新系统设置
export async function PUT(request: NextRequest) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, error: '权限不足' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const settings = updateSettingsSchema.parse(body);

    // 更新设置
    if (settings.registration_enabled !== undefined) {
      await SystemSettingsService.updateSetting(
        'registration_enabled', 
        settings.registration_enabled.toString()
      );
    }

    return NextResponse.json({
      success: true,
      message: '系统设置更新成功',
    });

  } catch (error: unknown) {
    console.error('更新系统设置错误:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.issues[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: '更新系统设置失败' },
      { status: 500 }
    );
  }
}

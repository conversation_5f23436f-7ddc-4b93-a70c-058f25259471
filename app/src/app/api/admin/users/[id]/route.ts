import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/database';
import { z } from 'zod';

// 验证管理员权限的中间件函数
async function verifyAdmin(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader) {
    return null;
  }

  try {
    const userInfo = JSON.parse(authHeader.replace('Bearer ', ''));
    if (userInfo.role !== 'admin') {
      return null;
    }
    return userInfo;
  } catch {
    return null;
  }
}

const updateUserSchema = z.object({
  username: z.string().min(3).max(50).optional(),
  email: z.string().optional(),
  role: z.enum(['admin', 'user']).optional(),
  auth_code: z.string().optional(),
});

const resetPasswordSchema = z.object({
  newPassword: z.string().min(6, '密码至少6个字符'),
});

// 更新用户信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, error: '权限不足' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const updates = updateUserSchema.parse(body);
    const resolvedParams = await params;

    const user = await UserService.updateUser(resolvedParams.id, updates);

    return NextResponse.json({
      success: true,
      data: user,
      message: '用户信息更新成功',
    });

  } catch (error: unknown) {
    console.error('更新用户错误:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.issues[0].message },
        { status: 400 }
      );
    }

    if (error && typeof error === 'object' && 'code' in error && error.code === '23505') {
      const dbError = error as { constraint?: string };
      if (dbError.constraint?.includes('username')) {
        return NextResponse.json(
          { success: false, error: '用户名已存在' },
          { status: 409 }
        );
      }
      if (dbError.constraint?.includes('email')) {
        return NextResponse.json(
          { success: false, error: '邮箱已被注册' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: '更新用户失败' },
      { status: 500 }
    );
  }
}

// 删除用户
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, error: '权限不足' },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    await UserService.deleteUser(resolvedParams.id);

    return NextResponse.json({
      success: true,
      message: '用户删除成功',
    });

  } catch (error) {
    console.error('删除用户错误:', error);
    return NextResponse.json(
      { success: false, error: '删除用户失败' },
      { status: 500 }
    );
  }
}

// 重置用户密码
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, error: '权限不足' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { newPassword } = resetPasswordSchema.parse(body);
    const resolvedParams = await params;

    await UserService.resetPassword(resolvedParams.id, newPassword);

    return NextResponse.json({
      success: true,
      message: '密码重置成功',
    });

  } catch (error) {
    console.error('重置密码错误:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.issues[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: '重置密码失败' },
      { status: 500 }
    );
  }
}

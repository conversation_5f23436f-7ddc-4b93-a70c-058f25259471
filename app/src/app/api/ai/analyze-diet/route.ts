import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createAIService } from '@/lib/ai-service';
import { UserSettingsService, UserService } from '@/lib/database';

const analyzeDietSchema = z.object({
  input: z.string().min(1, '输入内容不能为空'),
  inputType: z.enum(['text', 'image']),
  imageUrl: z.string().optional(),
});

// 分析饮食输入
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    const userInfo = JSON.parse(authHeader.replace('Bearer ', ''));
    const body = await request.json();

    // 验证请求数据
    const { input, inputType, imageUrl } = analyzeDietSchema.parse(body);

    // 获取最新的用户信息（包括auth_code）
    const currentUser = await UserService.getUserById(userInfo.id);
    if (!currentUser) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      );
    }

    // 获取用户设置
    const userSettings = await UserSettingsService.getUserSettings(userInfo.id);

    if (!userSettings || !userSettings.api_key) {
      return NextResponse.json(
        { success: false, error: '请先配置AI模型设置' },
        { status: 400 }
      );
    }

    // 创建AI服务实例
    const aiService = createAIService({
      baseURL: userSettings.api_base_url,
      apiKey: userSettings.api_key,
      authCode: currentUser.auth_code, // 使用数据库中的最新auth_code
      model: userSettings.ai_model,
    });

    // 根据输入类型进行分析
    let analysisResult;
    if (inputType === 'image' && imageUrl) {
      analysisResult = await aiService.analyzeDietImage(imageUrl, input);
    } else {
      analysisResult = await aiService.analyzeDietText(input);
    }

    return NextResponse.json({
      success: true,
      data: analysisResult,
    });

  } catch (error: unknown) {
    console.error('饮食分析错误:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.issues[0].message },
        { status: 400 }
      );
    }

    const errorMessage = error instanceof Error ? error.message : '饮食分析失败';
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { createAIService } from '@/lib/ai-service';
import { UserSettingsService, UserService } from '@/lib/database';

// 获取可用的AI模型列表
export async function GET(request: NextRequest) {
  try {
    // 从请求头获取用户信息
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    const userInfo = JSON.parse(authHeader.replace('Bearer ', ''));

    // 获取最新的用户信息（包括auth_code）
    const currentUser = await UserService.getUserById(userInfo.id);
    if (!currentUser) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      );
    }

    // 获取用户设置
    const userSettings = await UserSettingsService.getUserSettings(userInfo.id);

    if (!userSettings || !userSettings.api_key) {
      return NextResponse.json(
        { success: false, error: '请先配置AI模型设置' },
        { status: 400 }
      );
    }

    // 创建AI服务实例
    const aiService = createAIService({
      baseURL: userSettings.api_base_url,
      apiKey: userSettings.api_key,
      authCode: currentUser.auth_code, // 使用数据库中的最新auth_code
      model: userSettings.ai_model,
    });

    // 获取模型列表
    const models = await aiService.getAvailableModels();

    return NextResponse.json({
      success: true,
      data: models,
    });

  } catch (error) {
    console.error('获取模型列表错误:', error);
    return NextResponse.json(
      { success: false, error: '获取模型列表失败' },
      { status: 500 }
    );
  }
}

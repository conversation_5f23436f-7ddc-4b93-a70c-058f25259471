import { NextRequest, NextResponse } from 'next/server';
import { DietRecordService } from '@/lib/database';

// 删除饮食记录
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    const userInfo = JSON.parse(authHeader.replace('Bearer ', ''));
    const resolvedParams = await params;
    
    // 删除饮食记录
    await DietRecordService.deleteDietRecord(resolvedParams.id, userInfo.id);

    return NextResponse.json({
      success: true,
      message: '饮食记录删除成功',
    });

  } catch (error) {
    console.error('删除饮食记录错误:', error);
    return NextResponse.json(
      { success: false, error: '删除饮食记录失败' },
      { status: 500 }
    );
  }
}

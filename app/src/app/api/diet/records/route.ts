import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { DietRecordService, DishService } from '@/lib/database';

const createRecordSchema = z.object({
  original_input: z.string().min(1, '输入内容不能为空'),
  input_type: z.enum(['text', 'image']),
  image_url: z.string().optional(),
  meal_type: z.enum(['breakfast', 'lunch', 'dinner', 'snack']),
  record_time: z.string().optional(),
  total_calories: z.number(),
  ai_analysis: z.string(),
  dishes: z.array(z.object({
    name: z.string(),
    category: z.enum(['dish', 'staple', 'drink', 'side']),
  })),
  ingredients: z.array(z.object({
    name: z.string(),
    amount: z.number(),
    unit: z.string(),
    calories_per_100g: z.number().default(0),
  })),
});

// 获取用户的饮食记录
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    const userInfo = JSON.parse(authHeader.replace('Bearer ', ''));
    
    // 获取分页参数
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    
    // 获取用户的饮食记录
    const records = await DietRecordService.getUserDietRecords(userInfo.id, limit, offset);

    return NextResponse.json({
      success: true,
      data: records,
    });

  } catch (error) {
    console.error('获取饮食记录错误:', error);
    return NextResponse.json(
      { success: false, error: '获取饮食记录失败' },
      { status: 500 }
    );
  }
}

// 创建新的饮食记录
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    const userInfo = JSON.parse(authHeader.replace('Bearer ', ''));
    const body = await request.json();
    
    // 验证请求数据
    const recordData = createRecordSchema.parse(body);

    // 提取菜品信息并添加到菜品库
    if (recordData.dishes.length > 0) {
      await DishService.addDishes(recordData.dishes);
    }

    // 创建饮食记录
    const record = await DietRecordService.createDietRecord({
      user_id: userInfo.id,
      original_input: recordData.original_input,
      input_type: recordData.input_type,
      image_url: recordData.image_url,
      meal_type: recordData.meal_type,
      record_time: recordData.record_time,
      total_calories: recordData.total_calories,
      ai_analysis: recordData.ai_analysis,
      dishes: recordData.dishes.map(dish => dish.name),
      ingredients: recordData.ingredients,
    });

    return NextResponse.json({
      success: true,
      data: record,
      message: '饮食记录创建成功',
    });

  } catch (error: unknown) {
    console.error('创建饮食记录错误:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.issues[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: '创建饮食记录失败' },
      { status: 500 }
    );
  }
}

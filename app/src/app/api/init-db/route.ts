import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import bcrypt from 'bcryptjs';

// 初始化数据库
export async function POST(request: NextRequest) {
  try {
    // 直接尝试创建管理员用户
    const adminUsername = process.env.ADMIN_USERNAME || 'wrhsd';
    const adminPassword = process.env.ADMIN_PASSWORD || 'a123456';
    const passwordHash = await bcrypt.hash(adminPassword, 12);

    // 检查管理员用户是否已存在
    const { data: existingAdmin } = await supabaseAdmin
      .from('users')
      .select('id')
      .eq('username', adminUsername)
      .single();

    if (existingAdmin) {
      return NextResponse.json({
        success: true,
        message: '管理员用户已存在',
        data: {
          adminUsername,
          adminPassword,
        },
      });
    }

    // 创建管理员用户
    const { data: newAdmin, error } = await supabaseAdmin
      .from('users')
      .insert({
        username: adminUsername,
        email: '<EMAIL>',
        password_hash: passwordHash,
        role: 'admin',
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return NextResponse.json({
      success: true,
      message: '管理员用户创建成功',
      data: {
        adminUsername,
        adminPassword,
        userId: newAdmin.id,
      },
    });

  } catch (error: unknown) {
    console.error('数据库初始化错误:', error);
    const errorMessage = error instanceof Error ? error.message : '数据库初始化失败';

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}

// 检查数据库状态
export async function GET() {
  try {
    // 简单的数据库连接测试
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('count')
      .limit(1);

    return NextResponse.json({
      success: !error,
      data: {
        connected: !error,
        timestamp: new Date().toISOString(),
        error: error?.message,
      },
    });

  } catch (error: unknown) {
    console.error('数据库检查错误:', error);
    const errorMessage = error instanceof Error ? error.message : '数据库检查失败';

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}

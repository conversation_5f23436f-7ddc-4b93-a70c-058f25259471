import { NextRequest, NextResponse } from 'next/server';
import { imageUploadService } from '@/lib/image-upload';

// 上传图片
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取上传的文件
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { success: false, error: '请选择要上传的图片' },
        { status: 400 }
      );
    }

    // 验证文件
    const validation = imageUploadService.validateImageFile(file);
    if (!validation.valid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: 400 }
      );
    }

    // 上传图片
    const uploadResult = await imageUploadService.uploadImage(file);

    return NextResponse.json({
      success: true,
      data: uploadResult,
      message: '图片上传成功',
    });

  } catch (error: unknown) {
    console.error('图片上传错误:', error);
    
    const errorMessage = error instanceof Error ? error.message : '图片上传失败';
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}

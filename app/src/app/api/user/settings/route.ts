import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { UserSettingsService } from '@/lib/database';

const updateSettingsSchema = z.object({
  ai_model: z.string().min(1, 'AI模型不能为空'),
  api_base_url: z.string().url('API地址格式不正确'),
  api_key: z.string().min(1, 'API密钥不能为空'),
});

// 获取用户设置
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    const userInfo = JSON.parse(authHeader.replace('Bearer ', ''));
    
    // 获取用户设置
    const settings = await UserSettingsService.getUserSettings(userInfo.id);

    return NextResponse.json({
      success: true,
      data: settings,
    });

  } catch (error) {
    console.error('获取用户设置错误:', error);
    return NextResponse.json(
      { success: false, error: '获取用户设置失败' },
      { status: 500 }
    );
  }
}

// 更新用户设置
export async function PUT(request: NextRequest) {
  try {
    // 验证用户权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    const userInfo = JSON.parse(authHeader.replace('Bearer ', ''));
    const body = await request.json();
    
    // 验证请求数据
    const settingsData = updateSettingsSchema.parse(body);

    // 更新用户设置
    const settings = await UserSettingsService.updateUserSettings(userInfo.id, settingsData);

    return NextResponse.json({
      success: true,
      data: settings,
      message: '设置更新成功',
    });

  } catch (error: unknown) {
    console.error('更新用户设置错误:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.issues[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: '更新用户设置失败' },
      { status: 500 }
    );
  }
}

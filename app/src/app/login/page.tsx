import { AuthGuard } from '@/components/auth/AuthGuard';
import { LoginForm } from '@/components/auth/LoginForm';

export default function LoginPage() {
  return (
    <AuthGuard requireAuth={false}>
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              饮食热量分析系统
            </h1>
            <p className="text-gray-600">
              智能分析您的饮食热量，助您健康生活
            </p>
          </div>
          <LoginForm />
        </div>
      </div>
    </AuthGuard>
  );
}

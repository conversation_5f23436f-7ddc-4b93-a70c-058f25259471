'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CheckCircle, XCircle, Database, User } from 'lucide-react';

export default function SetupPage() {
  const [status, setStatus] = useState<{
    dbCheck: 'idle' | 'loading' | 'success' | 'error';
    adminCreate: 'idle' | 'loading' | 'success' | 'error';
    message: string;
  }>({
    dbCheck: 'idle',
    adminCreate: 'idle',
    message: '',
  });

  const checkDatabase = async () => {
    setStatus(prev => ({ ...prev, dbCheck: 'loading', message: '检查数据库连接...' }));
    
    try {
      const response = await fetch('/api/init-db');
      const data = await response.json();
      
      if (data.success && data.data.connected) {
        setStatus(prev => ({ 
          ...prev, 
          dbCheck: 'success', 
          message: '数据库连接正常！表结构已存在。' 
        }));
      } else {
        setStatus(prev => ({ 
          ...prev, 
          dbCheck: 'error', 
          message: `数据库连接失败：${data.data?.error || data.error}` 
        }));
      }
    } catch (error) {
      setStatus(prev => ({ 
        ...prev, 
        dbCheck: 'error', 
        message: '检查数据库时发生错误' 
      }));
    }
  };

  const createAdmin = async () => {
    setStatus(prev => ({ ...prev, adminCreate: 'loading', message: '创建管理员用户...' }));
    
    try {
      const response = await fetch('/api/init-db', { method: 'POST' });
      const data = await response.json();
      
      if (data.success) {
        setStatus(prev => ({ 
          ...prev, 
          adminCreate: 'success', 
          message: `管理员用户创建成功！用户名：${data.data.adminUsername}，密码：${data.data.adminPassword}` 
        }));
      } else {
        setStatus(prev => ({ 
          ...prev, 
          adminCreate: 'error', 
          message: `创建管理员失败：${data.error}` 
        }));
      }
    } catch (error) {
      setStatus(prev => ({ 
        ...prev, 
        adminCreate: 'error', 
        message: '创建管理员时发生错误' 
      }));
    }
  };

  const getStatusIcon = (state: string) => {
    switch (state) {
      case 'loading':
        return <Loader2 className="w-5 h-5 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle>饮食热量分析系统 - 初始化设置</CardTitle>
          <CardDescription>
            首次使用需要初始化数据库和创建管理员账号
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 数据库检查 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Database className="w-5 h-5" />
              步骤1：检查数据库连接
            </h3>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-sm text-yellow-800 mb-3">
                <strong>重要：</strong>在继续之前，请确保您已经在Supabase控制台中执行了数据库初始化脚本。
              </p>
              <details className="text-sm text-yellow-700">
                <summary className="cursor-pointer font-medium">点击查看需要执行的SQL脚本</summary>
                <pre className="mt-2 p-2 bg-yellow-100 rounded text-xs overflow-x-auto">
{`-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    auth_code VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('Asia/Shanghai', NOW()),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('Asia/Shanghai', NOW())
);

-- 其他表的创建脚本请参考 database/schema.sql 文件`}
                </pre>
              </details>
            </div>
            <div className="flex items-center gap-4">
              <Button 
                onClick={checkDatabase}
                disabled={status.dbCheck === 'loading'}
                variant="outline"
              >
                {getStatusIcon(status.dbCheck)}
                检查数据库
              </Button>
              {status.dbCheck !== 'idle' && (
                <span className={`text-sm ${
                  status.dbCheck === 'success' ? 'text-green-600' : 
                  status.dbCheck === 'error' ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {status.dbCheck === 'success' ? '✓ 数据库连接正常' :
                   status.dbCheck === 'error' ? '✗ 数据库连接失败' : '检查中...'}
                </span>
              )}
            </div>
          </div>

          {/* 管理员创建 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <User className="w-5 h-5" />
              步骤2：创建管理员账号
            </h3>
            <div className="flex items-center gap-4">
              <Button 
                onClick={createAdmin}
                disabled={status.adminCreate === 'loading' || status.dbCheck !== 'success'}
                variant={status.dbCheck === 'success' ? 'default' : 'secondary'}
              >
                {getStatusIcon(status.adminCreate)}
                创建管理员
              </Button>
              {status.adminCreate !== 'idle' && (
                <span className={`text-sm ${
                  status.adminCreate === 'success' ? 'text-green-600' : 
                  status.adminCreate === 'error' ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {status.adminCreate === 'success' ? '✓ 管理员创建成功' :
                   status.adminCreate === 'error' ? '✗ 管理员创建失败' : '创建中...'}
                </span>
              )}
            </div>
          </div>

          {/* 状态消息 */}
          {status.message && (
            <div className={`p-4 rounded-lg ${
              status.dbCheck === 'success' || status.adminCreate === 'success' 
                ? 'bg-green-50 border border-green-200 text-green-800'
                : 'bg-red-50 border border-red-200 text-red-800'
            }`}>
              <p className="text-sm">{status.message}</p>
            </div>
          )}

          {/* 完成后的操作 */}
          {status.adminCreate === 'success' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">设置完成！</h4>
              <p className="text-sm text-blue-800 mb-3">
                您现在可以使用管理员账号登录系统了。
              </p>
              <div className="flex gap-2">
                <Button 
                  onClick={() => window.location.href = '/login'}
                  size="sm"
                >
                  前往登录
                </Button>
                <Button 
                  onClick={() => window.location.href = '/dashboard'}
                  variant="outline"
                  size="sm"
                >
                  前往仪表板
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

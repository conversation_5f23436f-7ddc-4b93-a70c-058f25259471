'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import axios from 'axios';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Trash2, Key, <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Settings } from 'lucide-react';
import { useAuthStore } from '@/store/useAuthStore';
import { User } from '@/types';

const createUserSchema = z.object({
  username: z.string().min(3, '用户名至少3个字符').max(50, '用户名最多50个字符'),
  email: z.string().optional(),
  password: z.string().min(6, '密码至少6个字符'),
  role: z.enum(['admin', 'user']),
  auth_code: z.string().optional(),
});

const editAuthCodeSchema = z.object({
  auth_code: z.string().optional(),
});

const resetPasswordSchema = z.object({
  newPassword: z.string().min(6, '密码至少6个字符'),
});

type CreateUserFormData = z.infer<typeof createUserSchema>;
type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;
type EditAuthCodeFormData = z.infer<typeof editAuthCodeSchema>;

export function AdminDashboard() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [resetPasswordDialogOpen, setResetPasswordDialogOpen] = useState(false);
  const [editAuthCodeDialogOpen, setEditAuthCodeDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [creating, setCreating] = useState(false);
  const [resetting, setResetting] = useState(false);
  const [editingAuthCode, setEditingAuthCode] = useState(false);
  const [registrationEnabled, setRegistrationEnabled] = useState(true);
  const [updatingSettings, setUpdatingSettings] = useState(false);
  
  const { user } = useAuthStore();

  const {
    register: registerCreate,
    handleSubmit: handleCreateSubmit,
    formState: { errors: createErrors },
    reset: resetCreateForm,
  } = useForm<CreateUserFormData>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      role: 'user',
    },
  });

  const {
    register: registerReset,
    handleSubmit: handleResetSubmit,
    formState: { errors: resetErrors },
    reset: resetPasswordForm,
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
  });

  const {
    register: registerAuthCode,
    handleSubmit: handleAuthCodeSubmit,
    formState: { errors: authCodeErrors },
    reset: resetAuthCodeForm,
    setValue: setAuthCodeValue,
  } = useForm<EditAuthCodeFormData>({
    resolver: zodResolver(editAuthCodeSchema),
  });

  // 获取系统设置
  const fetchSystemSettings = async () => {
    if (!user) return;

    try {
      const response = await axios.get('/api/admin/settings', {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
        },
      });

      if (response.data.success) {
        setRegistrationEnabled(response.data.data.registration_enabled);
      }
    } catch (error: unknown) {
      console.error('获取系统设置错误:', error);
    }
  };

  // 获取用户列表
  const fetchUsers = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const response = await axios.get('/api/admin/users', {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
        },
      });

      if (response.data.success) {
        setUsers(response.data.data);
      } else {
        toast.error('获取用户列表失败');
      }
    } catch (error: unknown) {
      console.error('获取用户列表错误:', error);
      const axiosError = error as { response?: { data?: { error?: string } } };
      toast.error(axiosError.response?.data?.error || '获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 创建用户
  const onCreateUser = async (data: CreateUserFormData) => {
    if (!user) return;

    try {
      setCreating(true);
      const response = await axios.post('/api/admin/users', data, {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.data.success) {
        toast.success('用户创建成功');
        setUsers([response.data.data, ...users]);
        setCreateDialogOpen(false);
        resetCreateForm();
      } else {
        toast.error(response.data.error || '创建失败');
      }
    } catch (error: unknown) {
      console.error('创建用户错误:', error);
      const axiosError = error as { response?: { data?: { error?: string } } };
      toast.error(axiosError.response?.data?.error || '创建失败');
    } finally {
      setCreating(false);
    }
  };

  // 重置密码
  const onResetPassword = async (data: ResetPasswordFormData) => {
    if (!selectedUser || !user) return;

    try {
      setResetting(true);
      const response = await axios.patch(`/api/admin/users/${selectedUser.id}`, {
        newPassword: data.newPassword,
      }, {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.data.success) {
        toast.success('密码重置成功');
        setResetPasswordDialogOpen(false);
        setSelectedUser(null);
        resetPasswordForm();
      } else {
        toast.error(response.data.error || '重置失败');
      }
    } catch (error: unknown) {
      console.error('重置密码错误:', error);
      const axiosError = error as { response?: { data?: { error?: string } } };
      toast.error(axiosError.response?.data?.error || '重置失败');
    } finally {
      setResetting(false);
    }
  };

  // 删除用户
  const deleteUser = async (userId: string) => {
    if (!user) return;

    if (!confirm('确定要删除这个用户吗？此操作无法撤销。')) {
      return;
    }

    try {
      const response = await axios.delete(`/api/admin/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
        },
      });

      if (response.data.success) {
        toast.success('用户删除成功');
        setUsers(users.filter(u => u.id !== userId));
      } else {
        toast.error('删除失败');
      }
    } catch (error: unknown) {
      console.error('删除用户错误:', error);
      const axiosError = error as { response?: { data?: { error?: string } } };
      toast.error(axiosError.response?.data?.error || '删除失败');
    }
  };

  // 更新系统设置
  const updateSystemSettings = async (enabled: boolean) => {
    if (!user) return;

    try {
      setUpdatingSettings(true);
      const response = await axios.put('/api/admin/settings', {
        registration_enabled: enabled,
      }, {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.data.success) {
        setRegistrationEnabled(enabled);
        toast.success('系统设置更新成功');
      } else {
        toast.error(response.data.error || '更新失败');
      }
    } catch (error: unknown) {
      console.error('更新系统设置错误:', error);
      const axiosError = error as { response?: { data?: { error?: string } } };
      toast.error(axiosError.response?.data?.error || '更新失败');
    } finally {
      setUpdatingSettings(false);
    }
  };

  // 编辑授权码
  const onEditAuthCode = async (data: EditAuthCodeFormData) => {
    if (!selectedUser || !user) return;

    try {
      setEditingAuthCode(true);
      const response = await axios.put(`/api/admin/users/${selectedUser.id}`, {
        auth_code: data.auth_code,
      }, {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.data.success) {
        toast.success('授权码更新成功');
        setUsers(users.map(u =>
          u.id === selectedUser.id
            ? { ...u, auth_code: data.auth_code }
            : u
        ));
        setEditAuthCodeDialogOpen(false);
        setSelectedUser(null);
        resetAuthCodeForm();
      } else {
        toast.error(response.data.error || '更新失败');
      }
    } catch (error: unknown) {
      console.error('更新授权码错误:', error);
      const axiosError = error as { response?: { data?: { error?: string } } };
      toast.error(axiosError.response?.data?.error || '更新失败');
    } finally {
      setEditingAuthCode(false);
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchSystemSettings();
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          加载中...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">用户管理</h2>
          <p className="text-gray-600">管理系统用户和权限</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={fetchUsers}
            disabled={loading}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            创建用户
          </Button>
        </div>
      </div>

      {/* 系统设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            系统设置
          </CardTitle>
          <CardDescription>
            管理系统全局设置
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="registration-switch">允许用户注册</Label>
              <p className="text-sm text-gray-500">
                关闭后，新用户无法自行注册账号
              </p>
            </div>
            <Switch
              id="registration-switch"
              checked={registrationEnabled}
              onCheckedChange={updateSystemSettings}
              disabled={updatingSettings}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>用户列表</CardTitle>
          <CardDescription>
            系统中的所有用户
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>用户名</TableHead>
                  <TableHead className="hidden sm:table-cell">邮箱</TableHead>
                  <TableHead>角色</TableHead>
                  <TableHead className="hidden md:table-cell">授权码</TableHead>
                  <TableHead className="hidden lg:table-cell">创建时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((u) => (
                  <TableRow key={u.id}>
                    <TableCell className="font-medium">{u.username}</TableCell>
                    <TableCell className="hidden sm:table-cell">{u.email}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        u.role === 'admin'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {u.role === 'admin' ? '管理员' : '用户'}
                      </span>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">{u.auth_code || '-'}</TableCell>
                    <TableCell className="hidden lg:table-cell">
                      {format(new Date(u.created_at), 'yyyy-MM-dd HH:mm', { locale: zhCN })}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedUser(u);
                            setAuthCodeValue('auth_code', u.auth_code || '');
                            setEditAuthCodeDialogOpen(true);
                          }}
                          title="编辑授权码"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedUser(u);
                            setResetPasswordDialogOpen(true);
                          }}
                          title="重置密码"
                        >
                          <Key className="w-4 h-4" />
                        </Button>
                        {u.id !== user?.id && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteUser(u.id)}
                            title="删除用户"
                          >
                            <Trash2 className="w-4 h-4 text-red-500" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* 创建用户对话框 */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>创建新用户</DialogTitle>
            <DialogDescription>
              填写用户信息创建新账号
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleCreateSubmit(onCreateUser)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">用户名</Label>
              <Input
                id="username"
                {...registerCreate('username')}
                disabled={creating}
              />
              {createErrors.username && (
                <p className="text-sm text-red-500">{createErrors.username.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">邮箱（可选）</Label>
              <Input
                id="email"
                type="email"
                placeholder="可选填写"
                {...registerCreate('email')}
                disabled={creating}
              />
              {createErrors.email && (
                <p className="text-sm text-red-500">{createErrors.email.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">密码</Label>
              <Input
                id="password"
                type="password"
                {...registerCreate('password')}
                disabled={creating}
              />
              {createErrors.password && (
                <p className="text-sm text-red-500">{createErrors.password.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="auth_code">授权码（可选）</Label>
              <Input
                id="auth_code"
                {...registerCreate('auth_code')}
                disabled={creating}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setCreateDialogOpen(false)}
                disabled={creating}
              >
                取消
              </Button>
              <Button type="submit" disabled={creating}>
                {creating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    创建中...
                  </>
                ) : (
                  '创建用户'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* 重置密码对话框 */}
      <Dialog open={resetPasswordDialogOpen} onOpenChange={setResetPasswordDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>重置密码</DialogTitle>
            <DialogDescription>
              为用户 {selectedUser?.username} 设置新密码
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleResetSubmit(onResetPassword)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="newPassword">新密码</Label>
              <Input
                id="newPassword"
                type="password"
                {...registerReset('newPassword')}
                disabled={resetting}
              />
              {resetErrors.newPassword && (
                <p className="text-sm text-red-500">{resetErrors.newPassword.message}</p>
              )}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setResetPasswordDialogOpen(false)}
                disabled={resetting}
              >
                取消
              </Button>
              <Button type="submit" disabled={resetting}>
                {resetting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    重置中...
                  </>
                ) : (
                  '重置密码'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* 编辑授权码对话框 */}
      <Dialog open={editAuthCodeDialogOpen} onOpenChange={setEditAuthCodeDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑授权码</DialogTitle>
            <DialogDescription>
              为用户 {selectedUser?.username} 设置授权码
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAuthCodeSubmit(onEditAuthCode)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="auth_code">授权码</Label>
              <Input
                id="auth_code"
                placeholder="留空表示不使用授权码"
                {...registerAuthCode('auth_code')}
                disabled={editingAuthCode}
              />
              {authCodeErrors.auth_code && (
                <p className="text-sm text-red-500">{authCodeErrors.auth_code.message}</p>
              )}
              <p className="text-xs text-gray-500">
                授权码用于访问需要特殊权限的API服务
              </p>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setEditAuthCodeDialogOpen(false)}
                disabled={editingAuthCode}
              >
                取消
              </Button>
              <Button type="submit" disabled={editingAuthCode}>
                {editingAuthCode ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    更新中...
                  </>
                ) : (
                  '更新授权码'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}

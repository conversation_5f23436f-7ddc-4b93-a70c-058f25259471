'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LogOut, Settings, History, Plus } from 'lucide-react';
import { useAuthStore } from '@/store/useAuthStore';
import { useDietStore } from '@/store/useDietStore';
import { DietInputForm } from '@/components/diet/DietInputForm';
import { AnalysisResultForm } from '@/components/diet/AnalysisResultForm';
import { DietHistory } from '@/components/diet/DietHistory';
import { UserSettings } from '@/components/settings/UserSettings';

export function DashboardContent() {
  const [activeTab, setActiveTab] = useState<'input' | 'history' | 'settings'>('input');
  const router = useRouter();
  const { user, logout } = useAuthStore();
  const { analysisResult } = useDietStore();

  const handleLogout = () => {
    logout();
    router.push('/login');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                饮食热量分析系统
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                欢迎，{user?.username}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
              >
                <LogOut className="w-4 h-4 mr-2" />
                退出登录
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* 侧边栏导航 */}
          <aside className="w-full lg:w-64 space-y-2">
            <Card>
              <CardContent className="p-4">
                <nav className="space-y-2">
                  <Button
                    variant={activeTab === 'input' ? 'default' : 'ghost'}
                    className="w-full justify-start"
                    onClick={() => setActiveTab('input')}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    饮食记录
                  </Button>
                  
                  <Button
                    variant={activeTab === 'history' ? 'default' : 'ghost'}
                    className="w-full justify-start"
                    onClick={() => setActiveTab('history')}
                  >
                    <History className="w-4 h-4 mr-2" />
                    历史记录
                  </Button>
                  
                  <Button
                    variant={activeTab === 'settings' ? 'default' : 'ghost'}
                    className="w-full justify-start"
                    onClick={() => setActiveTab('settings')}
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    设置
                  </Button>
                </nav>
              </CardContent>
            </Card>

            {/* 管理员入口 */}
            {user?.role === 'admin' && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">管理员功能</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => router.push('/admin')}
                  >
                    用户管理
                  </Button>
                </CardContent>
              </Card>
            )}
          </aside>

          {/* 主要内容 */}
          <div className="flex-1">
            {activeTab === 'input' && (
              <div className="space-y-8">
                {!analysisResult ? (
                  <DietInputForm />
                ) : (
                  <AnalysisResultForm />
                )}
              </div>
            )}

            {activeTab === 'history' && (
              <DietHistory />
            )}

            {activeTab === 'settings' && (
              <UserSettings />
            )}
          </div>
        </div>
      </main>
    </div>
  );
}

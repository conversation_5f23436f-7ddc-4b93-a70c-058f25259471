'use client';

import { useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import axios from 'axios';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Trash2, Plus, Loader2 } from 'lucide-react';
import { useAuthStore } from '@/store/useAuthStore';
import { useDietStore } from '@/store/useDietStore';

const analysisResultSchema = z.object({
  ingredients: z.array(z.object({
    name: z.string().min(1, '食材名称不能为空'),
    amount: z.number().min(0.1, '数量必须大于0'),
    unit: z.string().min(1, '单位不能为空'),
  })),
  dishes: z.array(z.object({
    name: z.string().min(1, '菜品名称不能为空'),
    category: z.enum(['dish', 'staple', 'drink', 'side']),
  })),
  meal_type: z.enum(['breakfast', 'lunch', 'dinner', 'snack']),
  record_time: z.string(),
});

type AnalysisResultFormData = z.infer<typeof analysisResultSchema>;

export function AnalysisResultForm() {
  const { user } = useAuthStore();
  const { 
    analysisResult, 
    currentInput, 
    currentInputType, 
    currentImageUrl,
    setIsSubmitting,
    isSubmitting,
    reset: resetDietStore
  } = useDietStore();

  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<AnalysisResultFormData>({
    resolver: zodResolver(analysisResultSchema),
  });

  const { fields: ingredientFields, append: appendIngredient, remove: removeIngredient } = useFieldArray({
    control,
    name: 'ingredients',
  });

  const { fields: dishFields, append: appendDish, remove: removeDish } = useFieldArray({
    control,
    name: 'dishes',
  });

  // 初始化表单数据
  useEffect(() => {
    if (analysisResult) {
      setValue('ingredients', analysisResult.ingredients);
      setValue('dishes', analysisResult.dishes);
      setValue('meal_type', analysisResult.meal_type);
      setValue('record_time', analysisResult.estimated_time);
    }
  }, [analysisResult, setValue]);

  // 提交确认的分析结果
  const onSubmit = async (data: AnalysisResultFormData) => {
    if (!user) {
      toast.error('请先登录');
      return;
    }

    setIsSubmitting(true);

    try {
      // 先分析热量
      const calorieResponse = await axios.post('/api/ai/analyze-calories', {
        ingredients: data.ingredients,
      }, {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
          'Content-Type': 'application/json',
        },
      });

      if (!calorieResponse.data.success) {
        throw new Error('热量分析失败');
      }

      const calorieResult = calorieResponse.data.data;

      // 创建饮食记录
      const recordResponse = await axios.post('/api/diet/records', {
        original_input: currentInput,
        input_type: currentInputType,
        image_url: currentImageUrl,
        meal_type: data.meal_type,
        record_time: data.record_time,
        total_calories: calorieResult.total_calories,
        ai_analysis: calorieResult.analysis,
        dishes: data.dishes,
        ingredients: data.ingredients.map((ing, index) => ({
          ...ing,
          calories_per_100g: calorieResult.breakdown[index]?.calories || 0,
        })),
      }, {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
          'Content-Type': 'application/json',
        },
      });

      if (recordResponse.data.success) {
        toast.success('饮食记录保存成功');
        resetDietStore();
      } else {
        throw new Error(recordResponse.data.error || '保存失败');
      }

    } catch (error: unknown) {
      console.error('保存饮食记录错误:', error);
      const axiosError = error as { response?: { data?: { error?: string } } };
      toast.error(axiosError.response?.data?.error || '保存失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!analysisResult) {
    return null;
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>确认分析结果</CardTitle>
        <CardDescription>
          请检查并修改AI分析的结果，确保信息准确
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* 餐次和时间 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>餐次</Label>
              <Select
                value={watch('meal_type')}
                onValueChange={(value) => setValue('meal_type', value as 'breakfast' | 'lunch' | 'dinner' | 'snack')}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择餐次" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="breakfast">早餐</SelectItem>
                  <SelectItem value="lunch">午餐</SelectItem>
                  <SelectItem value="dinner">晚餐</SelectItem>
                  <SelectItem value="snack">加餐</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="record_time">记录时间</Label>
              <Input
                id="record_time"
                type="datetime-local"
                {...register('record_time')}
                disabled={isSubmitting}
              />
            </div>
          </div>

          {/* 食材列表 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-lg font-semibold">食材清单</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendIngredient({ name: '', amount: 0, unit: 'g' })}
              >
                <Plus className="w-4 h-4 mr-2" />
                添加食材
              </Button>
            </div>

            {ingredientFields.map((field, index) => (
              <div key={field.id} className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border rounded-lg">
                <div className="space-y-2">
                  <Label>食材名称</Label>
                  <Input
                    {...register(`ingredients.${index}.name`)}
                    placeholder="食材名称"
                    disabled={isSubmitting}
                  />
                  {errors.ingredients?.[index]?.name && (
                    <p className="text-sm text-red-500">{errors.ingredients[index]?.name?.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>数量</Label>
                  <Input
                    type="number"
                    step="0.1"
                    {...register(`ingredients.${index}.amount`, { valueAsNumber: true })}
                    placeholder="数量"
                    disabled={isSubmitting}
                  />
                  {errors.ingredients?.[index]?.amount && (
                    <p className="text-sm text-red-500">{errors.ingredients[index]?.amount?.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>单位</Label>
                  <Input
                    {...register(`ingredients.${index}.unit`)}
                    placeholder="单位"
                    disabled={isSubmitting}
                  />
                  {errors.ingredients?.[index]?.unit && (
                    <p className="text-sm text-red-500">{errors.ingredients[index]?.unit?.message}</p>
                  )}
                </div>

                <div className="flex items-end">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeIngredient(index)}
                    disabled={isSubmitting}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* 菜品列表 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-lg font-semibold">菜品清单</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendDish({ name: '', category: 'dish' })}
              >
                <Plus className="w-4 h-4 mr-2" />
                添加菜品
              </Button>
            </div>

            {dishFields.map((field, index) => (
              <div key={field.id} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg">
                <div className="space-y-2">
                  <Label>菜品名称</Label>
                  <Input
                    {...register(`dishes.${index}.name`)}
                    placeholder="菜品名称"
                    disabled={isSubmitting}
                  />
                  {errors.dishes?.[index]?.name && (
                    <p className="text-sm text-red-500">{errors.dishes[index]?.name?.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>分类</Label>
                  <Select
                    value={watch(`dishes.${index}.category`)}
                    onValueChange={(value) => setValue(`dishes.${index}.category`, value as 'dish' | 'staple' | 'drink' | 'side')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择分类" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="dish">菜品</SelectItem>
                      <SelectItem value="staple">主食</SelectItem>
                      <SelectItem value="drink">饮料</SelectItem>
                      <SelectItem value="side">配菜</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeDish(index)}
                    disabled={isSubmitting}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* 提交按钮 */}
          <div className="flex gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={resetDietStore}
              disabled={isSubmitting}
              className="flex-1"
            >
              取消
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  保存中...
                </>
              ) : (
                '确认保存'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

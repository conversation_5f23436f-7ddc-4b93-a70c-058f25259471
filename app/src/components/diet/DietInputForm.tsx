'use client';

import { useState, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import axios from 'axios';
import Image from 'next/image';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Camera, Upload, Loader2 } from 'lucide-react';
import { useAuthStore } from '@/store/useAuthStore';
import { useDietStore } from '@/store/useDietStore';

const dietInputSchema = z.object({
  input: z.string().min(1, '请输入饮食内容或上传图片'),
});

type DietInputFormData = z.infer<typeof dietInputSchema>;

export function DietInputForm() {
  const [inputType, setInputType] = useState<'text' | 'image'>('text');
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { user } = useAuthStore();
  const { setCurrentInput, setIsAnalyzing, isAnalyzing } = useDietStore();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<DietInputFormData>({
    resolver: zodResolver(dietInputSchema),
  });

  // 处理图片选择
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('只支持 JPEG、PNG、WebP 格式的图片');
        return;
      }

      // 验证文件大小 (10MB)
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        toast.error('图片大小不能超过 10MB');
        return;
      }

      setSelectedImage(file);
      
      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // 提交表单
  const onSubmit = async (data: DietInputFormData) => {
    if (!user) {
      toast.error('请先登录');
      return;
    }

    setIsAnalyzing(true);

    try {
      let imageUrl: string | undefined;

      // 如果是图片输入，先上传图片
      if (inputType === 'image' && selectedImage) {
        const formData = new FormData();
        formData.append('file', selectedImage);

        const uploadResponse = await axios.post('/api/upload/image', formData, {
          headers: {
            'Authorization': `Bearer ${JSON.stringify(user)}`,
            'Content-Type': 'multipart/form-data',
          },
        });

        if (uploadResponse.data.success) {
          imageUrl = uploadResponse.data.data.thumbnailUrl;
        } else {
          throw new Error('图片上传失败');
        }
      }

      // 调用AI分析
      const analysisResponse = await axios.post('/api/ai/analyze-diet', {
        input: data.input,
        inputType,
        imageUrl,
      }, {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
          'Content-Type': 'application/json',
        },
      });

      if (analysisResponse.data.success) {
        // 保存分析结果到store
        setCurrentInput(data.input, inputType, imageUrl);
        useDietStore.getState().setAnalysisResult(analysisResponse.data.data);
        
        toast.success('分析完成');
        
        // 重置表单
        reset();
        setSelectedImage(null);
        setImagePreview(null);
        setInputType('text');
      } else {
        throw new Error(analysisResponse.data.error || '分析失败');
      }

    } catch (error: unknown) {
      console.error('饮食分析错误:', error);
      const axiosError = error as { response?: { data?: { error?: string } } };
      toast.error(axiosError.response?.data?.error || '分析失败，请稍后重试');
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>饮食记录</CardTitle>
        <CardDescription>
          输入您的饮食内容或上传食物图片，AI将为您分析食材和热量
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* 输入类型选择 */}
          <div className="flex gap-4">
            <Button
              type="button"
              variant={inputType === 'text' ? 'default' : 'outline'}
              onClick={() => setInputType('text')}
              className="flex-1"
            >
              文字输入
            </Button>
            <Button
              type="button"
              variant={inputType === 'image' ? 'default' : 'outline'}
              onClick={() => setInputType('image')}
              className="flex-1"
            >
              <Camera className="w-4 h-4 mr-2" />
              图片上传
            </Button>
          </div>

          {/* 文字输入 */}
          {inputType === 'text' && (
            <div className="space-y-2">
              <Label htmlFor="input">饮食描述</Label>
              <Textarea
                id="input"
                placeholder="请描述您吃了什么，例如：一碗米饭，红烧肉，青菜..."
                {...register('input')}
                disabled={isAnalyzing}
                rows={4}
              />
              {errors.input && (
                <p className="text-sm text-red-500">{errors.input.message}</p>
              )}
            </div>
          )}

          {/* 图片输入 */}
          {inputType === 'image' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="input">补充说明（可选）</Label>
                <Input
                  id="input"
                  placeholder="可以补充说明食物的具体信息..."
                  {...register('input')}
                  disabled={isAnalyzing}
                />
              </div>

              <div className="space-y-2">
                <Label>上传食物图片</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  {imagePreview ? (
                    <div className="space-y-4">
                      <Image
                        src={imagePreview}
                        alt="预览"
                        width={400}
                        height={300}
                        className="max-w-full max-h-64 mx-auto rounded-lg object-contain"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setSelectedImage(null);
                          setImagePreview(null);
                          if (fileInputRef.current) {
                            fileInputRef.current.value = '';
                          }
                        }}
                      >
                        重新选择
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Upload className="w-12 h-12 mx-auto text-gray-400" />
                      <div>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                        >
                          选择图片
                        </Button>
                        <p className="text-sm text-gray-500 mt-2">
                          支持 JPEG、PNG、WebP 格式，最大 10MB
                        </p>
                      </div>
                    </div>
                  )}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/jpeg,image/jpg,image/png,image/webp"
                    onChange={handleImageSelect}
                    className="hidden"
                  />
                </div>
              </div>
            </div>
          )}

          {/* 提交按钮 */}
          <Button 
            type="submit" 
            className="w-full" 
            disabled={isAnalyzing || (inputType === 'image' && !selectedImage)}
          >
            {isAnalyzing ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                分析中...
              </>
            ) : (
              '开始分析'
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}

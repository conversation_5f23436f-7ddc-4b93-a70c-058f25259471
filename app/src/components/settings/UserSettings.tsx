'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import axios from 'axios';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, RefreshCw, TestTube } from 'lucide-react';
import { useAuthStore } from '@/store/useAuthStore';


const settingsSchema = z.object({
  ai_model: z.string().min(1, 'AI模型不能为空'),
  api_base_url: z.string().url('API地址格式不正确'),
  api_key: z.string().min(1, 'API密钥不能为空'),
});

type SettingsFormData = z.infer<typeof settingsSchema>;

export function UserSettings() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testingConnection, setTestingConnection] = useState(false);
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [loadingModels, setLoadingModels] = useState(false);
  
  const { user } = useAuthStore();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<SettingsFormData>({
    resolver: zodResolver(settingsSchema),
    defaultValues: {
      ai_model: 'gpt-4o',
      api_base_url: 'https://api.openai.com',
      api_key: '',
    },
  });

  // 获取用户设置
  const fetchSettings = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const response = await axios.get('/api/user/settings', {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
        },
      });

      if (response.data.success && response.data.data) {
        const settings = response.data.data;
        reset({
          ai_model: settings.ai_model,
          api_base_url: settings.api_base_url,
          api_key: settings.api_key,
        });
      }
    } catch (error: unknown) {
      console.error('获取设置错误:', error);
      // 如果没有设置，使用默认值，不显示错误
    } finally {
      setLoading(false);
    }
  };

  // 获取可用模型
  const fetchAvailableModels = async () => {
    if (!user) return;

    try {
      setLoadingModels(true);

      const response = await axios.get('/api/ai/models', {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
        },
      });

      if (response.data.success) {
        setAvailableModels(response.data.data);
        toast.success(`成功获取 ${response.data.data.length} 个模型`);
      } else {
        toast.error(response.data.error || '获取模型列表失败');
      }
    } catch (error: unknown) {
      console.error('获取模型列表错误:', error);
      const axiosError = error as { response?: { data?: { error?: string } } };
      toast.error(axiosError.response?.data?.error || '获取模型列表失败，请检查API配置');
    } finally {
      setLoadingModels(false);
    }
  };

  // 测试连接
  const testConnection = async () => {
    if (!user) return;

    const formData = watch();
    
    try {
      setTestingConnection(true);
      
      // 先保存设置
      await axios.put('/api/user/settings', formData, {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
          'Content-Type': 'application/json',
        },
      });

      // 然后测试连接
      const response = await axios.get('/api/ai/models', {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
        },
      });

      if (response.data.success) {
        toast.success('连接测试成功');
        setAvailableModels(response.data.data);
      } else {
        toast.error('连接测试失败');
      }
    } catch (error: unknown) {
      console.error('测试连接错误:', error);
      const axiosError = error as { response?: { data?: { error?: string } } };
      toast.error(axiosError.response?.data?.error || '连接测试失败');
    } finally {
      setTestingConnection(false);
    }
  };

  // 保存设置
  const onSubmit = async (data: SettingsFormData) => {
    if (!user) return;

    try {
      setSaving(true);
      const response = await axios.put('/api/user/settings', data, {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.data.success) {
        toast.success('设置保存成功');
        // 重新获取设置以确保表单数据同步
        await fetchSettings();
      } else {
        toast.error(response.data.error || '保存失败');
      }
    } catch (error: unknown) {
      console.error('保存设置错误:', error);
      const axiosError = error as { response?: { data?: { error?: string } } };
      toast.error(axiosError.response?.data?.error || '保存失败');
    } finally {
      setSaving(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          加载中...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">设置</h2>
        <p className="text-gray-600">配置您的AI模型和API设置</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>AI模型配置</CardTitle>
          <CardDescription>
            配置用于饮食分析的AI模型和API设置
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* API基础URL */}
            <div className="space-y-2">
              <Label htmlFor="api_base_url">API基础URL</Label>
              <Input
                id="api_base_url"
                type="url"
                placeholder="https://api.openai.com"
                {...register('api_base_url')}
                disabled={saving}
              />
              {errors.api_base_url && (
                <p className="text-sm text-red-500">{errors.api_base_url.message}</p>
              )}
            </div>

            {/* API密钥 */}
            <div className="space-y-2">
              <Label htmlFor="api_key">API密钥</Label>
              <Input
                id="api_key"
                type="password"
                placeholder="输入您的API密钥"
                {...register('api_key')}
                disabled={saving}
              />
              {errors.api_key && (
                <p className="text-sm text-red-500">{errors.api_key.message}</p>
              )}
            </div>

            {/* 测试连接按钮 */}
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={testConnection}
                disabled={testingConnection || saving}
              >
                {testingConnection ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    测试中...
                  </>
                ) : (
                  <>
                    <TestTube className="w-4 h-4 mr-2" />
                    测试连接
                  </>
                )}
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={fetchAvailableModels}
                disabled={loadingModels || saving}
              >
                {loadingModels ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    获取中...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    获取模型列表
                  </>
                )}
              </Button>
            </div>

            {/* AI模型选择 */}
            <div className="space-y-2">
              <Label>AI模型</Label>
              <Select
                value={watch('ai_model')}
                onValueChange={(value) => setValue('ai_model', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择AI模型" />
                </SelectTrigger>
                <SelectContent>
                  {availableModels.length > 0 ? (
                    availableModels.map((model) => (
                      <SelectItem key={model} value={model}>
                        {model}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no-models" disabled>
                      请先测试连接获取模型列表
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              {errors.ai_model && (
                <p className="text-sm text-red-500">{errors.ai_model.message}</p>
              )}
            </div>

            {/* 保存按钮 */}
            <Button type="submit" disabled={saving} className="w-full">
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  保存中...
                </>
              ) : (
                '保存设置'
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

import axios from 'axios';
import { AIAnalysisResult, CalorieAnalysisResult } from '@/types';

export interface AIConfig {
  baseURL: string;
  apiKey: string;
  authCode?: string;
  model: string;
}

export class AIService {
  private config: AIConfig;

  constructor(config: AIConfig) {
    this.config = config;
  }

  // 构建API URL
  private buildApiUrl(endpoint: string): string {
    const baseUrl = this.config.baseURL.replace(/\/$/, '');

    // 如果是您的网关，使用授权码作为路径参数
    if (baseUrl.includes('gateway.988886.xyz')) {
      if (this.config.authCode && this.config.authCode.trim()) {
        return `${baseUrl}/${this.config.authCode}${endpoint}`;
      }
      // 如果没有授权码，尝试使用API密钥
      return `${baseUrl}/${this.config.apiKey}${endpoint}`;
    }

    // 如果有授权码，使用授权码
    if (this.config.authCode && this.config.authCode.trim()) {
      return `${baseUrl}/${this.config.authCode}${endpoint}`;
    }

    return `${baseUrl}${endpoint}`;
  }

  // 获取可用模型列表
  async getAvailableModels(): Promise<string[]> {
    const baseUrl = this.config.baseURL.replace(/\/$/, '');

    // 如果是您的网关，使用正确的格式
    if (baseUrl.includes('gateway.988886.xyz')) {
      try {
        const url = `${baseUrl}/${this.config.authCode || this.config.apiKey}/v1/models`;

        const response = await axios.get(url, {
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json',
          },
        });

        // 处理不同的响应格式
        if (response.data && Array.isArray(response.data)) {
          return response.data;
        }

        if (response.data && response.data.data) {
          return response.data.data.map((model: { id: string }) => model.id);
        }

        return [];
      } catch (error) {
        console.error('获取模型列表失败:', error);
        return [];
      }
    }

    // 标准OpenAI格式
    try {
      const url = this.buildApiUrl('/v1/models');

      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.data && response.data.data) {
        return response.data.data.map((model: { id: string }) => model.id);
      }
      return [];
    } catch (error) {
      console.error('获取模型列表失败:', error);
      return [];
    }
  }

  // 分析饮食输入（文字）
  async analyzeDietText(input: string): Promise<AIAnalysisResult> {
    const prompt = `
请分析以下饮食描述，提取出具体的食材、数量和菜品信息。请严格按照JSON格式返回，不要包含任何其他文字：

用户输入：${input}

请返回JSON格式：
{
  "ingredients": [
    {
      "name": "食材名称",
      "amount": 数量(数字),
      "unit": "单位(g/ml/个等)"
    }
  ],
  "dishes": [
    {
      "name": "菜品名称",
      "category": "dish|staple|drink|side"
    }
  ],
  "meal_type": "breakfast|lunch|dinner|snack",
  "estimated_time": "YYYY-MM-DD HH:mm:ss"
}

注意：
1. 根据当前北京时间判断餐次
2. 尽量准确估算食材重量
3. 菜品分类：dish(菜品)、staple(主食)、drink(饮料)、side(配菜)
4. 只返回JSON，不要其他解释
`;

    try {
      const response = await axios.post(
        this.buildApiUrl('/v1/chat/completions'),
        {
          model: this.config.model,
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
          temperature: 0.3,
          max_tokens: 1000,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      // 增强错误处理和调试信息
      console.log('AI API Response:', JSON.stringify(response.data, null, 2));

      // 处理流式响应格式
      let responseData = response.data;
      if (typeof responseData === 'string') {
        // 如果是流式响应，解析最后一个完整的JSON对象
        const lines = responseData.split('\n').filter(line => line.trim());
        const lastDataLine = lines.reverse().find(line => line.startsWith('data: ') && !line.includes('data: {"choices":[{"delta"'));
        if (lastDataLine) {
          try {
            responseData = JSON.parse(lastDataLine.replace('data: ', ''));
          } catch (e) {
            console.error('解析流式响应失败:', e);
            throw new Error('AI API响应格式错误，请检查API配置');
          }
        } else {
          throw new Error('AI API响应格式错误，请检查API配置');
        }
      }

      if (!responseData || !responseData.choices || responseData.choices.length === 0) {
        console.error('AI API响应格式错误:', responseData);
        throw new Error('AI API响应格式错误，请检查API配置');
      }

      const content = responseData.choices[0].message?.content;
      if (!content) {
        console.error('AI API返回内容为空:', responseData.choices[0]);
        throw new Error('AI API返回内容为空');
      }

      // 处理包含代码块的响应
      let jsonContent = content;
      if (content.includes('```json')) {
        const match = content.match(/```json\n([\s\S]*?)\n```/);
        if (match) {
          jsonContent = match[1];
        }
      }

      return JSON.parse(jsonContent);
    } catch (error) {
      console.error('AI分析失败:', error);
      if (error instanceof SyntaxError) {
        throw new Error('AI返回的JSON格式错误，请稍后重试');
      }
      if (error instanceof Error && error.message.includes('AI API')) {
        throw error;
      }
      throw new Error('AI分析失败，请稍后重试');
    }
  }

  // 分析饮食输入（图片）
  async analyzeDietImage(imageUrl: string, additionalText?: string): Promise<AIAnalysisResult> {
    const prompt = `
请分析这张食物图片，识别出具体的食材、数量和菜品信息。${additionalText ? `用户补充说明：${additionalText}` : ''}

请严格按照JSON格式返回，不要包含任何其他文字：

{
  "ingredients": [
    {
      "name": "食材名称",
      "amount": 数量(数字),
      "unit": "单位(g/ml/个等)"
    }
  ],
  "dishes": [
    {
      "name": "菜品名称",
      "category": "dish|staple|drink|side"
    }
  ],
  "meal_type": "breakfast|lunch|dinner|snack",
  "estimated_time": "YYYY-MM-DD HH:mm:ss"
}

注意：
1. 根据当前北京时间判断餐次
2. 根据图片中食物的视觉大小估算重量
3. 菜品分类：dish(菜品)、staple(主食)、drink(饮料)、side(配菜)
4. 只返回JSON，不要其他解释
`;

    try {
      const response = await axios.post(
        this.buildApiUrl('/v1/chat/completions'),
        {
          model: this.config.model,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: prompt,
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: imageUrl,
                  },
                },
              ],
            },
          ],
          temperature: 0.3,
          max_tokens: 1000,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      // 增强错误处理和调试信息
      console.log('AI API Response (Image):', JSON.stringify(response.data, null, 2));

      // 处理流式响应格式
      let responseData = response.data;
      if (typeof responseData === 'string') {
        // 如果是流式响应，解析最后一个完整的JSON对象
        const lines = responseData.split('\n').filter(line => line.trim());
        const lastDataLine = lines.reverse().find(line => line.startsWith('data: ') && !line.includes('data: {"choices":[{"delta"'));
        if (lastDataLine) {
          try {
            responseData = JSON.parse(lastDataLine.replace('data: ', ''));
          } catch (e) {
            console.error('解析流式响应失败:', e);
            throw new Error('AI API响应格式错误，请检查API配置');
          }
        } else {
          throw new Error('AI API响应格式错误，请检查API配置');
        }
      }

      if (!responseData || !responseData.choices || responseData.choices.length === 0) {
        console.error('AI API响应格式错误:', responseData);
        throw new Error('AI API响应格式错误，请检查API配置');
      }

      const content = responseData.choices[0].message?.content;
      if (!content) {
        console.error('AI API返回内容为空:', responseData.choices[0]);
        throw new Error('AI API返回内容为空');
      }

      // 处理包含代码块的响应
      let jsonContent = content;
      if (content.includes('```json')) {
        const match = content.match(/```json\n([\s\S]*?)\n```/);
        if (match) {
          jsonContent = match[1];
        }
      }

      return JSON.parse(jsonContent);
    } catch (error) {
      console.error('AI图片分析失败:', error);
      if (error instanceof SyntaxError) {
        throw new Error('AI返回的JSON格式错误，请稍后重试');
      }
      if (error instanceof Error && error.message.includes('AI API')) {
        throw error;
      }
      throw new Error('AI图片分析失败，请稍后重试');
    }
  }

  // 分析热量和营养
  async analyzeCalories(ingredients: { name: string; amount: number; unit: string }[]): Promise<CalorieAnalysisResult> {
    const ingredientsList = ingredients
      .map(ing => `${ing.name} ${ing.amount}${ing.unit}`)
      .join('、');

    const prompt = `
请分析以下食材的热量和营养信息：

食材清单：${ingredientsList}

请严格按照JSON格式返回，不要包含任何其他文字：

{
  "total_calories": 总热量(数字),
  "analysis": "简短的营养分析和建议(50字以内)",
  "breakdown": [
    {
      "ingredient": "食材名称",
      "calories": 该食材热量(数字)
    }
  ]
}

注意：
1. 热量计算要准确，基于实际营养数据
2. 分析要简洁实用
3. 只返回JSON，不要其他解释
`;

    try {
      const response = await axios.post(
        this.buildApiUrl('/v1/chat/completions'),
        {
          model: this.config.model,
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
          temperature: 0.3,
          max_tokens: 800,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      // 增强错误处理和调试信息
      console.log('AI API Response (Calories):', JSON.stringify(response.data, null, 2));

      // 处理流式响应格式
      let responseData = response.data;
      if (typeof responseData === 'string') {
        // 如果是流式响应，解析最后一个完整的JSON对象
        const lines = responseData.split('\n').filter(line => line.trim());
        const lastDataLine = lines.reverse().find(line => line.startsWith('data: ') && !line.includes('data: {"choices":[{"delta"'));
        if (lastDataLine) {
          try {
            responseData = JSON.parse(lastDataLine.replace('data: ', ''));
          } catch (e) {
            console.error('解析流式响应失败:', e);
            throw new Error('AI API响应格式错误，请检查API配置');
          }
        } else {
          throw new Error('AI API响应格式错误，请检查API配置');
        }
      }

      if (!responseData || !responseData.choices || responseData.choices.length === 0) {
        console.error('AI API响应格式错误:', responseData);
        throw new Error('AI API响应格式错误，请检查API配置');
      }

      const content = responseData.choices[0].message?.content;
      if (!content) {
        console.error('AI API返回内容为空:', responseData.choices[0]);
        throw new Error('AI API返回内容为空');
      }

      // 处理包含代码块的响应
      let jsonContent = content;
      if (content.includes('```json')) {
        const match = content.match(/```json\n([\s\S]*?)\n```/);
        if (match) {
          jsonContent = match[1];
        }
      }

      return JSON.parse(jsonContent);
    } catch (error) {
      console.error('热量分析失败:', error);
      if (error instanceof SyntaxError) {
        throw new Error('AI返回的JSON格式错误，请稍后重试');
      }
      if (error instanceof Error && error.message.includes('AI API')) {
        throw error;
      }
      throw new Error('热量分析失败，请稍后重试');
    }
  }
}

// 创建AI服务实例的工厂函数
export function createAIService(config: AIConfig): AIService {
  return new AIService(config);
}

// 默认配置
export const getDefaultAIConfig = (): AIConfig => ({
  baseURL: process.env.DEFAULT_AI_BASE_URL || 'https://api.openai.com',
  apiKey: process.env.DEFAULT_AI_API_KEY || '',
  model: 'gpt-4o',
});

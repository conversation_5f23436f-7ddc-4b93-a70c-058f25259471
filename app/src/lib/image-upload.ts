import axios from 'axios';

export interface ImageUploadResult {
  thumbnailUrl: string;
  originalUrl?: string;
}

export class ImageUploadService {
  private uploadUrl: string;
  private uploadPath: string;

  constructor() {
    this.uploadUrl = process.env.IMAGE_UPLOAD_URL || 'https://xrea.988886.xyz/webdav/fixed-upload-new.php';
    this.uploadPath = process.env.IMAGE_UPLOAD_PATH || 'l-weight';
  }

  // 上传图片文件
  async uploadImage(file: File): Promise<ImageUploadResult> {
    try {
      // 创建FormData
      const formData = new FormData();
      formData.append('path', this.uploadPath);
      formData.append('thumbnail', 'true');
      formData.append('original', 'false');
      formData.append('image', 'false');
      formData.append('file', file);

      const response = await axios.post(this.uploadUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data && response.data.thumbnailResult) {
        return {
          thumbnailUrl: response.data.thumbnailResult,
          originalUrl: response.data.originalResult,
        };
      } else {
        throw new Error('上传响应格式错误');
      }
    } catch (error) {
      console.error('图片上传失败:', error);
      throw new Error('图片上传失败，请稍后重试');
    }
  }

  // 验证图片文件
  validateImageFile(file: File): { valid: boolean; error?: string } {
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: '只支持 JPEG、PNG、WebP 格式的图片',
      };
    }

    // 检查文件大小 (10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        valid: false,
        error: '图片大小不能超过 10MB',
      };
    }

    return { valid: true };
  }

  // 压缩图片（可选功能）
  async compressImage(file: File, maxWidth = 1920, quality = 0.8): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // 计算新尺寸
        let { width, height } = img;
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }

        canvas.width = width;
        canvas.height = height;

        // 绘制压缩后的图片
        ctx?.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              reject(new Error('图片压缩失败'));
            }
          },
          file.type,
          quality
        );
      };

      img.onerror = () => reject(new Error('图片加载失败'));
      img.src = URL.createObjectURL(file);
    });
  }
}

// 创建图片上传服务实例
export const imageUploadService = new ImageUploadService();

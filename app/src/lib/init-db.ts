import { supabaseAdmin } from './supabase';
import bcrypt from 'bcryptjs';

// 初始化数据库
export async function initializeDatabase() {
  try {
    console.log('开始初始化数据库...');

    // 检查管理员用户是否存在
    const { data: existingAdmin } = await supabaseAdmin
      .from('users')
      .select('id')
      .eq('username', process.env.ADMIN_USERNAME || 'wrhsd')
      .single();

    if (!existingAdmin) {
      // 创建管理员用户
      const adminPassword = process.env.ADMIN_PASSWORD || 'a123456';
      const passwordHash = await bcrypt.hash(adminPassword, 12);

      const { error: adminError } = await supabaseAdmin
        .from('users')
        .insert({
          username: process.env.ADMIN_USERNAME || 'wrhsd',
          email: '<EMAIL>',
          password_hash: passwordHash,
          role: 'admin',
        });

      if (adminError) {
        console.error('创建管理员用户失败:', adminError);
      } else {
        console.log('管理员用户创建成功');
      }
    } else {
      console.log('管理员用户已存在');
    }

    // 初始化一些基础菜品数据
    const basicDishes = [
      { name: '米饭', category: 'staple' },
      { name: '面条', category: 'staple' },
      { name: '馒头', category: 'staple' },
      { name: '面包', category: 'staple' },
      { name: '白开水', category: 'drink' },
      { name: '茶', category: 'drink' },
      { name: '咖啡', category: 'drink' },
      { name: '牛奶', category: 'drink' },
      { name: '鸡蛋', category: 'side' },
      { name: '青菜', category: 'side' },
    ];

    const { error: dishError } = await supabaseAdmin
      .from('dishes')
      .upsert(basicDishes, { onConflict: 'name,category' });

    if (dishError) {
      console.error('初始化菜品数据失败:', dishError);
    } else {
      console.log('基础菜品数据初始化成功');
    }

    console.log('数据库初始化完成');
  } catch (error) {
    console.error('数据库初始化失败:', error);
    throw error;
  }
}

// 检查数据库连接
export async function checkDatabaseConnection() {
  try {
    const { error } = await supabaseAdmin
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      console.error('数据库连接失败:', error);
      return false;
    }

    console.log('数据库连接正常');
    return true;
  } catch (error) {
    console.error('数据库连接检查失败:', error);
    return false;
  }
}

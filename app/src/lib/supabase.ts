import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-anon-key';
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-service-role-key';

// 检查环境变量是否正确配置
const isConfigured =
  process.env.NEXT_PUBLIC_SUPABASE_URL &&
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&
  process.env.SUPABASE_SERVICE_ROLE_KEY &&
  !process.env.NEXT_PUBLIC_SUPABASE_URL.includes('placeholder') &&
  !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.includes('placeholder') &&
  !process.env.SUPABASE_SERVICE_ROLE_KEY.includes('placeholder');

// 客户端 Supabase 实例（用于前端）
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 服务端 Supabase 实例（用于服务端操作，具有更高权限）
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);

// 导出配置状态
export { isConfigured };

// 数据库表名常量
export const TABLES = {
  USERS: 'users',
  USER_SETTINGS: 'user_settings',
  DIET_RECORDS: 'diet_records',
  INGREDIENTS: 'ingredients',
  DISHES: 'dishes',
} as const;

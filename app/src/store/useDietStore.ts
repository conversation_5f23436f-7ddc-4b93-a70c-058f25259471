import { create } from 'zustand';
import { DietRecord, AIAnalysisResult, CalorieAnalysisResult } from '@/types';

interface DietState {
  // 当前分析的饮食记录
  currentInput: string;
  currentInputType: 'text' | 'image';
  currentImageUrl?: string;
  
  // 分析结果
  analysisResult: AIAnalysisResult | null;
  calorieResult: CalorieAnalysisResult | null;
  
  // 历史记录
  dietRecords: DietRecord[];
  
  // 加载状态
  isAnalyzing: boolean;
  isSubmitting: boolean;
  
  // 操作方法
  setCurrentInput: (input: string, type: 'text' | 'image', imageUrl?: string) => void;
  setAnalysisResult: (result: AIAnalysisResult | null) => void;
  setCalorieResult: (result: CalorieAnalysisResult | null) => void;
  setDietRecords: (records: DietRecord[]) => void;
  addDietRecord: (record: DietRecord) => void;
  removeDietRecord: (id: string) => void;
  setIsAnalyzing: (isAnalyzing: boolean) => void;
  setIsSubmitting: (isSubmitting: boolean) => void;
  reset: () => void;
}

export const useDietStore = create<DietState>((set) => ({
  // 初始状态
  currentInput: '',
  currentInputType: 'text',
  currentImageUrl: undefined,
  analysisResult: null,
  calorieResult: null,
  dietRecords: [],
  isAnalyzing: false,
  isSubmitting: false,
  
  // 方法
  setCurrentInput: (input, type, imageUrl) => set({ 
    currentInput: input, 
    currentInputType: type,
    currentImageUrl: imageUrl
  }),
  
  setAnalysisResult: (result) => set({ analysisResult: result }),
  
  setCalorieResult: (result) => set({ calorieResult: result }),
  
  setDietRecords: (records) => set({ dietRecords: records }),
  
  addDietRecord: (record) => set((state) => ({ 
    dietRecords: [record, ...state.dietRecords] 
  })),
  
  removeDietRecord: (id) => set((state) => ({ 
    dietRecords: state.dietRecords.filter(record => record.id !== id) 
  })),
  
  setIsAnalyzing: (isAnalyzing) => set({ isAnalyzing }),
  
  setIsSubmitting: (isSubmitting) => set({ isSubmitting }),
  
  reset: () => set({ 
    currentInput: '',
    currentInputType: 'text',
    currentImageUrl: undefined,
    analysisResult: null,
    calorieResult: null,
    isAnalyzing: false,
    isSubmitting: false
  })
}));

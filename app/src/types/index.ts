// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
  auth_code?: string;
  created_at: string;
  updated_at: string;
}

// 用户设置类型
export interface UserSettings {
  id: string;
  user_id: string;
  ai_model: string;
  api_base_url: string;
  api_key: string;
  created_at: string;
  updated_at: string;
}

// 饮食记录类型
export interface DietRecord {
  id: string;
  user_id: string;
  original_input: string;
  input_type: 'text' | 'image';
  image_url?: string;
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  record_time: string;
  total_calories: number;
  ai_analysis: string;
  created_at: string;
  updated_at: string;
  ingredients: Ingredient[];
  dishes: string[];
}

// 食材类型
export interface Ingredient {
  id: string;
  diet_record_id: string;
  name: string;
  amount: number;
  unit: string;
  calories_per_100g: number;
}

// 菜品库类型
export interface Dish {
  id: string;
  name: string;
  category: 'dish' | 'staple' | 'drink' | 'side';
  created_at: string;
  updated_at: string;
}

// AI分析结果类型
export interface AIAnalysisResult {
  ingredients: {
    name: string;
    amount: number;
    unit: string;
  }[];
  dishes: {
    name: string;
    category: 'dish' | 'staple' | 'drink' | 'side';
  }[];
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  estimated_time: string;
}

// 热量分析结果类型
export interface CalorieAnalysisResult {
  total_calories: number;
  analysis: string;
  breakdown: {
    ingredient: string;
    calories: number;
  }[];
}

// API响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 表单类型
export interface DietInputForm {
  input: string;
  image?: File;
  meal_type?: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  record_time?: string;
}

export interface LoginForm {
  username: string;
  password: string;
}

export interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface UserManagementForm {
  username: string;
  email: string;
  password?: string;
  auth_code?: string;
  role: 'admin' | 'user';
}

export interface SettingsForm {
  ai_model: string;
  api_base_url: string;
  api_key: string;
}

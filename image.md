图床使用示例：

async function uploadFile() {
    const url = 'https://xrea.988886.xyz/webdav/fixed-upload-new.php'; //图床后台
    const postData = new URLSearchParams({
           path: 'l-weight',
        thumbnail: true,
        original: false,
        image: false,
        file=@/root/test/111/66f0cabf92ade7143.png
    });

    try {
        const response = await fetch(url, {
            method: 'POST',
            body: postData
        });

        // 解析响应体
        const result = await response.json();

        // 打印响应到控制台
        console.log('Response:', result);
    } catch (error) {
        console.error('Error:', error);
    }
}

// 调用函数
uploadFile();

则只保存缩略图
Response JSON: {'thumbnailResult': 'https://wrhcn001.588886.xyz/webdav/files/uploads/ttrss/thumbnails/940e4f8a-9c8e-49bc-8635-e4127a015934.jpg'}

其中thumbnailResult为缩略图的外链。